/*
 * Globalize Culture bg-BG
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "bg-BG", "default", {
	name: "bg-BG",
	englishName: "Bulgarian (Bulgaria)",
	nativeName: "български (България)",
	language: "bg",
	numberFormat: {
		",": " ",
		".": ",",
		negativeInfinity: "- безкрайност",
		positiveInfinity: "+ безкрайност",
		percent: {
			",": " ",
			".": ","
		},
		currency: {
			pattern: ["-n $","n $"],
			",": " ",
			".": ",",
			symbol: "лв."
		}
	},
	calendars: {
		standard: {
			"/": ".",
			firstDay: 1,
			days: {
				names: ["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"],
				namesAbbr: ["нед","пон","вт","ср","четв","пет","съб"],
				namesShort: ["н","п","в","с","ч","п","с"]
			},
			months: {
				names: ["януари","февруари","март","април","май","юни","юли","август","септември","октомври","ноември","декември",""],
				namesAbbr: ["ян","февр","март","апр","май","юни","юли","авг","септ","окт","ноември","дек",""]
			},
			AM: null,
			PM: null,
			eras: [{"name":"след новата ера","start":null,"offset":0}],
			patterns: {
				d: "d.M.yyyy 'г.'",
				D: "dd MMMM yyyy 'г.'",
				t: "HH:mm 'ч.'",
				T: "HH:mm:ss 'ч.'",
				f: "dd MMMM yyyy 'г.' HH:mm 'ч.'",
				F: "dd MMMM yyyy 'г.' HH:mm:ss 'ч.'",
				M: "dd MMMM",
				Y: "MMMM yyyy 'г.'"
			}
		}
	}
});

}( this ));
