import { useState, useRef, useEffect } from 'react';
import { useTaskStore } from '../lib/store';
import { parseTaskCommand } from '../lib/openrouter-config';
import { Send, X, Bot, User, Minimize2, Maximize2 } from 'lucide-react';

export default function AIAssistant({ isOpen, onClose }) {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Hi! I\'m your task assistant. I can help you create, move, and manage your tasks. Try saying "Create a new task called Design Homepage" or "Move task X to Done".'
    }
  ]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef(null);
  const { tasks, addTask, updateTask, deleteTask, findTaskByName } = useTaskStore();

  const getColumnDisplayName = (column) => {
    const columnMap = {
      'todo': 'To Do',
      'inprogress': 'In Progress',
      'done': 'Done'
    };
    return columnMap[column] || column;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleTaskCommand = async (command) => {
    try {
      switch (command.action) {
        case 'create':
          await addTask({
            title: command.taskName,
            description: '',
            status: command.column || 'todo',
            priority: 'medium'
          });
          return `✅ Task "${command.taskName}" created successfully in the ${getColumnDisplayName(command.column || 'todo')} column.`;

        case 'move':
          const taskToMove = findTaskByName(command.taskName);
          if (!taskToMove) {
            return `❌ I couldn't find a task with the name "${command.taskName}". Please check the task name and try again.`;
          }
          await updateTask(taskToMove.id, { status: command.column });
          return `✅ Task "${taskToMove.title}" moved to ${getColumnDisplayName(command.column)} successfully.`;

        case 'delete':
          const taskToDelete = findTaskByName(command.taskName);
          if (!taskToDelete) {
            return `❌ I couldn't find a task with the name "${command.taskName}". Please check the task name and try again.`;
          }
          await deleteTask(taskToDelete.id);
          return `✅ Task "${taskToDelete.title}" deleted successfully.`;

        default:
          return await handleGeneralQuery(command.message);
      }
    } catch (error) {
      console.error('Error executing task command:', error);
      return 'Sorry, I encountered an error while processing your request. Please try again.';
    }
  };

  const handleGeneralQuery = async (message) => {
    try {
      const taskCount = tasks.length;
      const todoCount = tasks.filter(t => t.status === 'todo').length;
      const inProgressCount = tasks.filter(t => t.status === 'inprogress').length;
      const doneCount = tasks.filter(t => t.status === 'done').length;

      const response = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          context: {
            taskCount,
            todoCount,
            inProgressCount,
            doneCount,
            tasks: tasks.map(t => ({ title: t.title, status: t.status, priority: t.priority }))
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Error calling AI API:', error);

      // Fallback to local responses for common queries
      const lowerMessage = message.toLowerCase();
      const taskCount = tasks.length;
      const todoCount = tasks.filter(t => t.status === 'todo').length;
      const inProgressCount = tasks.filter(t => t.status === 'inprogress').length;
      const doneCount = tasks.filter(t => t.status === 'done').length;

      if (lowerMessage.includes('how many') || lowerMessage.includes('count')) {
        return `📊 You currently have ${taskCount} total tasks: ${todoCount} in To Do, ${inProgressCount} in Progress, and ${doneCount} completed.`;
      }

      if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
        return '🤖 I can help you with:\n• Creating tasks: "Create a new task called [name]"\n• Moving tasks: "Move task [name] to [To Do/In Progress/Done]"\n• Deleting tasks: "Delete task [name]"\n• Getting task summaries and priorities\n• Answering questions about your tasks';
      }

      return '🤖 I\'m here to help with your tasks! You can ask me to create, move, or delete tasks, or ask about your current task status.';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: input.trim()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    try {
      const command = parseTaskCommand(input.trim());
      const response = await handleTaskCommand(command);

      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: response
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error processing message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed right-6 top-20 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl z-50 transition-all duration-300 ${
      isMinimized ? 'w-80 h-16' : 'w-96 h-[32rem]'
    } backdrop-blur-sm`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary/5 to-accent/5 dark:from-primary/10 dark:to-accent/10 rounded-t-xl">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center shadow-sm">
            <Bot className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-sm">AI Assistant</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">Ready to help with your tasks</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title={isMinimized ? 'Expand' : 'Minimize'}
          >
            {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </button>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Close"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages Area */}
          <div className="flex-1 p-4 h-80 overflow-y-auto bg-gray-50/50 dark:bg-gray-900/50">
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-3 max-w-[85%] ${
                    message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm ${
                      message.type === 'user'
                        ? 'bg-gradient-to-br from-primary to-primary/80'
                        : 'bg-gradient-to-br from-accent to-accent/80'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="h-4 w-4 text-white" />
                      ) : (
                        <Bot className="h-4 w-4 text-white" />
                      )}
                    </div>
                    <div className={`px-4 py-3 rounded-2xl text-sm shadow-sm ${
                      message.type === 'user'
                        ? 'bg-primary text-primary-foreground rounded-br-md'
                        : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600 rounded-bl-md'
                    }`}>
                      <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                    </div>
                  </div>
                </div>
              ))}
              {loading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center shadow-sm">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input Area */}
          <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="flex space-x-3">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask me to create, move, or manage tasks..."
                className="flex-1 px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all"
                disabled={loading}
              />
              <button
                type="submit"
                disabled={loading || !input.trim()}
                className="px-4 py-3 bg-gradient-to-r from-primary to-primary/90 text-primary-foreground rounded-xl hover:from-primary/90 hover:to-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm"
                title="Send message"
              >
                <Send className="h-4 w-4" />
              </button>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-2 mt-3">
              <button
                type="button"
                onClick={() => setInput('Create a new task called ')}
                className="px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Create task
              </button>
              <button
                type="button"
                onClick={() => setInput('How many tasks do I have?')}
                className="px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Task count
              </button>
              <button
                type="button"
                onClick={() => setInput('What should I focus on today?')}
                className="px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Focus help
              </button>
            </div>
          </form>
        </>
      )}
    </div>
  );
}
