/*
 * Globalize Culture af
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "af", "default", {
	name: "af",
	englishName: "Afrikaans",
	nativeName: "Afrikaans",
	language: "af",
	numberFormat: {
		percent: {
			pattern: ["-n%","n%"]
		},
		currency: {
			pattern: ["$-n","$ n"],
			symbol: "R"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["Sondag","<PERSON><PERSON><PERSON>","<PERSON><PERSON>dag","<PERSON><PERSON><PERSON>dag","<PERSON><PERSON>dag","<PERSON>rydag","<PERSON>terdag"],
				namesAbbr: ["<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>oe<PERSON>","<PERSON>d","<PERSON>ry","<PERSON>t"],
				names<PERSON>hort: ["So","Ma","Di","Wo","Do","Vr","<PERSON>"]
			},
			months: {
				names: ["<PERSON>uarie","<PERSON>ruarie","Maart","April","<PERSON>","<PERSON>ie","<PERSON>","<PERSON>","September","Oktober","November","Desember",""],
				names<PERSON>bbr: ["<PERSON>","Feb","Mar","Apr","Mei","Jun","Jul","Aug","Sep","Okt","Nov","Des",""]
			},
			patterns: {
				d: "yyyy/MM/dd",
				D: "dd MMMM yyyy",
				t: "hh:mm tt",
				T: "hh:mm:ss tt",
				f: "dd MMMM yyyy hh:mm tt",
				F: "dd MMMM yyyy hh:mm:ss tt",
				M: "dd MMMM",
				Y: "MMMM yyyy"
			}
		}
	}
});

}( this ));
