/*
 * Globalize Culture sw
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "sw", "default", {
	name: "sw",
	englishName: "Kiswahili",
	nativeName: "Kiswahili",
	language: "sw",
	numberFormat: {
		currency: {
			symbol: "S"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],
				namesAbbr: ["<PERSON><PERSON><PERSON>.","<PERSON><PERSON>.","<PERSON><PERSON>.","<PERSON><PERSON>.","<PERSON>h.","<PERSON><PERSON>.","Juma<PERSON>."],
				namesShort: ["P","T","N","T","A","<PERSON>","<PERSON>"]
			},
			months: {
				names: ["<PERSON>ua<PERSON>","<PERSON>ruari","<PERSON><PERSON>","Aprili","<PERSON>","<PERSON>i","<PERSON><PERSON>","A<PERSON><PERSON>","Septemba","<PERSON>toba","<PERSON>em<PERSON>","<PERSON>em<PERSON>",""],
				names<PERSON>bbr: ["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","Jul","Ago","Sep","Okt","Nov","Dec",""]
			}
		}
	}
});

}( this ));
