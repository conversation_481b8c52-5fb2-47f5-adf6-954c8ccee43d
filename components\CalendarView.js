import { useState, useMemo } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import { useTaskStore } from '../lib/store';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { Modal } from './ui/Modal';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  AlertCircle,
  CheckCircle,
  Circle
} from 'lucide-react';
import 'react-big-calendar/lib/css/react-big-calendar.css';
const localizer = momentLocalizer(moment);
function TaskEventComponent({ event }) {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'done':
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'in-progress':
        return <Clock className="w-3 h-3 text-yellow-500" />;
      default:
        return <Circle className="w-3 h-3 text-blue-500" />;
    }
  };
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50';
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-green-500 bg-green-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };
  return (
    <div className={`p-1 border-l-4 rounded text-xs ${getPriorityColor(event.priority)}`}>
      <div className="flex items-center space-x-1">
        {getStatusIcon(event.status)}
        <span className="font-medium truncate">{event.title}</span>
      </div>
    </div>
  );
}
function TaskDetailsModal({ task, isOpen, onClose }) {
  if (!task) return null;
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Task Details">
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold text-lg">{task.title}</h3>
          {task.description && (
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              {task.description}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant={task.priority}>{task.priority}</Badge>
          <Badge variant="outline">{task.status.replace('-', ' ')}</Badge>
        </div>
        {task.deadline && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <CalendarIcon className="w-4 h-4" />
            <span>Due: {moment(task.deadline).format('MMMM D, YYYY')}</span>
          </div>
        )}
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <p>Created: {moment(task.createdAt).format('MMMM D, YYYY [at] h:mm A')}</p>
          {task.updatedAt && (
            <p>Updated: {moment(task.updatedAt).format('MMMM D, YYYY [at] h:mm A')}</p>
          )}
        </div>
      </div>
    </Modal>
  );
}
export default function CalendarView() {
  const { tasks, currentProject } = useTaskStore();
  const [selectedTask, setSelectedTask] = useState(null);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [currentView, setCurrentView] = useState('month');
  const [currentDate, setCurrentDate] = useState(new Date());
  const projectTasks = tasks.filter(task => task.project === currentProject);
  const events = useMemo(() => {
    return projectTasks
      .filter(task => task.deadline)
      .map(task => ({
        id: task.id,
        title: task.title,
        start: new Date(task.deadline),
        end: new Date(task.deadline),
        allDay: true,
        resource: task,
        priority: task.priority,
        status: task.status,
        description: task.description
      }));
  }, [projectTasks]);
  const handleSelectEvent = (event) => {
    setSelectedTask(event.resource);
    setShowTaskModal(true);
  };
  const handleNavigate = (date) => {
    setCurrentDate(date);
  };
  const handleViewChange = (view) => {
    setCurrentView(view);
  };
  const eventStyleGetter = (event) => {
    let backgroundColor = '#3174ad';
    let borderColor = '#3174ad';
    switch (event.priority) {
      case 'high':
        backgroundColor = '#dc2626';
        borderColor = '#dc2626';
        break;
      case 'medium':
        backgroundColor = '#d97706';
        borderColor = '#d97706';
        break;
      case 'low':
        backgroundColor = '#059669';
        borderColor = '#059669';
        break;
    }
    if (event.status === 'done') {
      backgroundColor = '#6b7280';
      borderColor = '#6b7280';
    }
    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        fontSize: '12px'
      }
    };
  };
  const upcomingTasks = projectTasks
    .filter(task => {
      if (!task.deadline) return false;
      const deadline = new Date(task.deadline);
      const today = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(today.getDate() + 7);
      return deadline >= today && deadline <= nextWeek && task.status !== 'done';
    })
    .sort((a, b) => new Date(a.deadline) - new Date(b.deadline));
  const overdueTasks = projectTasks
    .filter(task => {
      if (!task.deadline) return false;
      const deadline = new Date(task.deadline);
      const today = new Date();
      return deadline < today && task.status !== 'done';
    })
    .sort((a, b) => new Date(a.deadline) - new Date(b.deadline));
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Calendar View
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          View your tasks and deadlines in calendar format
        </p>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-4">
              <div style={{ height: '600px' }}>
                <Calendar
                  localizer={localizer}
                  events={events}
                  startAccessor="start"
                  endAccessor="end"
                  onSelectEvent={handleSelectEvent}
                  onNavigate={handleNavigate}
                  onView={handleViewChange}
                  view={currentView}
                  date={currentDate}
                  eventPropGetter={eventStyleGetter}
                  components={{
                    event: TaskEventComponent
                  }}
                  popup
                  showMultiDayTimes
                  step={60}
                  showAllEvents
                />
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-6">
          {overdueTasks.length > 0 && (
            <Card className="border-red-200 bg-red-50 dark:bg-red-900/20">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center space-x-2 text-red-800 dark:text-red-200">
                  <AlertCircle className="w-4 h-4" />
                  <span>Overdue Tasks</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {overdueTasks.slice(0, 5).map(task => (
                    <div
                      key={task.id}
                      className="p-2 bg-white dark:bg-gray-800 rounded border cursor-pointer hover:shadow-sm"
                      onClick={() => {
                        setSelectedTask(task);
                        setShowTaskModal(true);
                      }}
                    >
                      <p className="font-medium text-sm">{task.title}</p>
                      <p className="text-xs text-red-600 dark:text-red-400">
                        Due: {moment(task.deadline).format('MMM D, YYYY')}
                      </p>
                    </div>
                  ))}
                  {overdueTasks.length > 5 && (
                    <p className="text-xs text-red-600 dark:text-red-400 text-center">
                      +{overdueTasks.length - 5} more overdue tasks
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CalendarIcon className="w-4 h-4" />
                <span>Upcoming This Week</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {upcomingTasks.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  No upcoming deadlines this week
                </p>
              ) : (
                <div className="space-y-2">
                  {upcomingTasks.map(task => (
                    <div
                      key={task.id}
                      className="p-2 border border-gray-200 dark:border-gray-700 rounded cursor-pointer hover:shadow-sm"
                      onClick={() => {
                        setSelectedTask(task);
                        setShowTaskModal(true);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">{task.title}</p>
                        <Badge variant={task.priority} className="text-xs">
                          {task.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Due: {moment(task.deadline).format('MMM D, YYYY')}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Legend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-600 rounded"></div>
                  <span>High Priority</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-600 rounded"></div>
                  <span>Medium Priority</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-600 rounded"></div>
                  <span>Low Priority</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-gray-600 rounded"></div>
                  <span>Completed</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <TaskDetailsModal
        task={selectedTask}
        isOpen={showTaskModal}
        onClose={() => {
          setShowTaskModal(false);
          setSelectedTask(null);
        }}
      />
    </div>
  );
}
