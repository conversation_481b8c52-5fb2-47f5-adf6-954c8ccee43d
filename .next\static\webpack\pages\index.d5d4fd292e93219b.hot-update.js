"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/VoiceCommands.js":
/*!*************************************!*\
  !*** ./components/VoiceCommands.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VoiceCommands; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Mic,MicOff,Volume2!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,CheckCircle,Mic,MicOff,Volume2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VoiceCommands() {\n    _s();\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcript, setTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confidence, setConfidence] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const recognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { addTask, addTodoTask, currentProject } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            if (SpeechRecognition) {\n                setIsSupported(true);\n                recognitionRef.current = new SpeechRecognition();\n                const recognition = recognitionRef.current;\n                recognition.continuous = false;\n                recognition.interimResults = true;\n                recognition.lang = \"en-US\";\n                recognition.onstart = ()=>{\n                    setIsListening(true);\n                    setTranscript(\"\");\n                };\n                recognition.onresult = (event)=>{\n                    let finalTranscript = \"\";\n                    let interimTranscript = \"\";\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript;\n                        const confidence = event.results[i][0].confidence;\n                        if (event.results[i].isFinal) {\n                            finalTranscript += transcript;\n                            setConfidence(confidence);\n                        } else {\n                            interimTranscript += transcript;\n                        }\n                    }\n                    setTranscript(finalTranscript || interimTranscript);\n                    if (finalTranscript) {\n                        processVoiceCommand(finalTranscript, confidence);\n                    }\n                };\n                recognition.onerror = (event)=>{\n                    console.error(\"Speech recognition error:\", event.error);\n                    setIsListening(false);\n                    let errorMessage = \"Voice recognition error occurred\";\n                    switch(event.error){\n                        case \"no-speech\":\n                            errorMessage = \"No speech detected. Please try again.\";\n                            break;\n                        case \"audio-capture\":\n                            errorMessage = \"Microphone not accessible. Please check permissions.\";\n                            break;\n                        case \"not-allowed\":\n                            errorMessage = \"Microphone permission denied. Please enable microphone access.\";\n                            break;\n                        case \"network\":\n                            errorMessage = \"Network error occurred. Please check your connection.\";\n                            break;\n                    }\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(errorMessage);\n                };\n                recognition.onend = ()=>{\n                    setIsListening(false);\n                };\n            }\n        }\n        return ()=>{\n            if (recognitionRef.current) {\n                recognitionRef.current.abort();\n            }\n        };\n    }, []);\n    const processVoiceCommand = (command, confidence)=>{\n        const lowerCommand = command.toLowerCase().trim();\n        if (confidence < 0.7) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Low confidence in voice recognition. Please try again.\");\n            return;\n        }\n        if (lowerCommand.includes(\"create task\") || lowerCommand.includes(\"add task\") || lowerCommand.includes(\"new task\")) {\n            const taskTitle = extractTaskTitle(lowerCommand);\n            if (taskTitle) {\n                const priority = extractPriority(lowerCommand);\n                const deadline = extractDeadline(lowerCommand);\n                addTask({\n                    title: taskTitle,\n                    description: 'Created via voice command: \"'.concat(command, '\"'),\n                    priority: priority || \"medium\",\n                    deadline: deadline || \"\",\n                    status: \"todo\",\n                    project: currentProject\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('Task \"'.concat(taskTitle, '\" created successfully!'));\n                speak(\"Task \".concat(taskTitle, \" has been created\"));\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Could not extract task title from voice command\");\n                speak(\"Sorry, I could not understand the task title\");\n            }\n        } else if (lowerCommand.includes(\"add todo\") || lowerCommand.includes(\"quick todo\")) {\n            const todoTitle = extractTodoTitle(lowerCommand);\n            if (todoTitle) {\n                addTodoTask({\n                    title: todoTitle\n                });\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('Todo \"'.concat(todoTitle, '\" added successfully!'));\n                speak(\"Todo \".concat(todoTitle, \" has been added\"));\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Could not extract todo title from voice command\");\n                speak(\"Sorry, I could not understand the todo title\");\n            }\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Voice command not recognized. Try saying \"Create task [task name]\" or \"Add todo [todo item]\"');\n            speak(\"Sorry, I did not understand that command\");\n        }\n    };\n    const extractTaskTitle = (command)=>{\n        const patterns = [\n            /create task (?:called |named |titled )?(.+?)(?:\\s+with|\\s+priority|\\s+due|$)/i,\n            /add task (?:called |named |titled )?(.+?)(?:\\s+with|\\s+priority|\\s+due|$)/i,\n            /new task (?:called |named |titled )?(.+?)(?:\\s+with|\\s+priority|\\s+due|$)/i\n        ];\n        for (const pattern of patterns){\n            const match = command.match(pattern);\n            if (match && match[1]) {\n                return match[1].trim();\n            }\n        }\n        return null;\n    };\n    const extractTodoTitle = (command)=>{\n        const patterns = [\n            /add todo (.+?)$/i,\n            /quick todo (.+?)$/i\n        ];\n        for (const pattern of patterns){\n            const match = command.match(pattern);\n            if (match && match[1]) {\n                return match[1].trim();\n            }\n        }\n        return null;\n    };\n    const extractPriority = (command)=>{\n        if (command.includes(\"high priority\") || command.includes(\"urgent\")) return \"high\";\n        if (command.includes(\"low priority\")) return \"low\";\n        if (command.includes(\"medium priority\") || command.includes(\"normal priority\")) return \"medium\";\n        return null;\n    };\n    const extractDeadline = (command)=>{\n        if (command.includes(\"due today\") || command.includes(\"today\")) {\n            return new Date().toISOString().split(\"T\")[0];\n        }\n        if (command.includes(\"due tomorrow\") || command.includes(\"tomorrow\")) {\n            const tomorrow = new Date();\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            return tomorrow.toISOString().split(\"T\")[0];\n        }\n        return null;\n    };\n    const speak = (text)=>{\n        if (\"speechSynthesis\" in window) {\n            const utterance = new SpeechSynthesisUtterance(text);\n            utterance.rate = 0.8;\n            utterance.pitch = 1;\n            utterance.volume = 0.8;\n            speechSynthesis.speak(utterance);\n        }\n    };\n    const startListening = ()=>{\n        if (recognitionRef.current && !isListening) {\n            try {\n                recognitionRef.current.start();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Voice recognition started. Speak now...\");\n            } catch (error) {\n                console.error(\"Error starting recognition:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Failed to start voice recognition\");\n            }\n        }\n    };\n    const stopListening = ()=>{\n        if (recognitionRef.current && isListening) {\n            recognitionRef.current.stop();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].info(\"Voice recognition stopped\");\n        }\n    };\n    if (!isSupported) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.AlertCircle, {\n                        className: \"w-5 h-5 text-yellow-600\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium text-yellow-800 dark:text-yellow-200\",\n                                children: \"Voice Commands Not Supported\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                children: \"Your browser doesn't support the Web Speech API. Try using Chrome or Edge.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                lineNumber: 196,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950/20 dark:to-indigo-950/20 border border-purple-100 dark:border-purple-900/30 rounded-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Volume2, {\n                                    className: \"w-3 h-3 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold text-purple-800 dark:text-purple-200\",\n                                        children: \"Voice Commands\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-600 dark:text-purple-300\",\n                                        children: \"Hands-free task creation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                        variant: \"secondary\",\n                        className: \"text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300\",\n                        children: \"Beta\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: isListening ? stopListening : startListening,\n                        variant: isListening ? \"destructive\" : \"default\",\n                        size: \"sm\",\n                        className: \"flex items-center space-x-2\",\n                        children: isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.MicOff, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Stop Listening\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Mic, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Voice Command\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: \"Listening...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            transcript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Mic_MicOff_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CheckCircle, {\n                            className: \"w-4 h-4 text-green-500 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                    children: \"Recognized:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        '\"',\n                                        transcript,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                confidence > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-500 mt-1\",\n                                    children: [\n                                        \"Confidence: \",\n                                        Math.round(confidence * 100),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-xs text-blue-600 dark:text-blue-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium mb-1\",\n                        children: \"Supported commands:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• \"Create task [task name]\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• \"Add task [task name] with high priority\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• \"New task [task name] due tomorrow\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• \"Add todo [todo item]\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '• \"Quick todo [todo item]\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\VoiceCommands.js\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n_s(VoiceCommands, \"qCL+D1/+OsaYIFeAhmq/sL4dKUw=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_2__.useTaskStore\n    ];\n});\n_c = VoiceCommands;\nvar _c;\n$RefreshReg$(_c, \"VoiceCommands\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/VoiceCommands.js\n"));

/***/ })

});