import { useState } from 'react';
import {
  DndContext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  useDroppable,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTaskStore, useTimeTrackingStore } from '../lib/store';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { Modal } from './ui/Modal';
import { Input } from './ui/Input';
import {
  ClockIcon,
  CalendarIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  CircleStackIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { formatDate } from '../lib/utils';
import { exportTasksToCSV, exportTasksToPDF } from '../utils/exportData';
import toast from 'react-hot-toast';
function TaskCard({ task }) {
  const { updateTask, deleteTask } = useTaskStore();
  const { activeTimer, startTimer, stopTimer } = useTimeTrackingStore();
  const [showEdit, setShowEdit] = useState(false);
  const [editTask, setEditTask] = useState(task);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const handleEdit = () => {
    updateTask(task.id, editTask);
    setShowEdit(false);
    toast.success('Task updated successfully');
  };
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      deleteTask(task.id);
      toast.success('Task deleted successfully');
    }
  };
  const handleTimer = () => {
    if (activeTimer?.taskId === task.id) {
      stopTimer();
      toast.success('Timer stopped');
    } else {
      startTimer(task.id, task.title);
      toast.success('Timer started');
    }
  };
  const isTimerActive = activeTimer?.taskId === task.id;
  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className={`mb-3 ${isDragging ? 'opacity-50 rotate-2 scale-105' : ''} transition-all duration-200`}
      >
        <Card className="cursor-move hover:shadow-lg hover:-translate-y-1 transition-all duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 group">
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <h4 className="font-semibold text-gray-900 dark:text-white text-sm leading-tight pr-2">
                {task.title}
              </h4>
              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTimer();
                  }}
                >
                  {isTimerActive ? (
                    <PauseIcon className="h-3 w-3 text-red-500" />
                  ) : (
                    <PlayIcon className="h-3 w-3 text-green-500" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowEdit(true);
                  }}
                >
                  <PencilIcon className="h-3 w-3 text-gray-500" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete();
                  }}
                >
                  <TrashIcon className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {task.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {task.description}
              </p>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge
                  variant={task.priority}
                  className={`text-xs font-medium ${
                    task.priority === 'high' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                    task.priority === 'medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                  }`}
                >
                  {task.priority === 'high' ? '🔴' : task.priority === 'medium' ? '🟡' : '🟢'} {task.priority}
                </Badge>
                {task.deadline && (
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <CalendarIcon className="h-3 w-3 mr-1" />
                    {formatDate(task.deadline)}
                  </div>
                )}
              </div>
              {isTimerActive && (
                <div className="flex items-center text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20 px-2 py-1 rounded-full">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  Active
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      <Modal
        isOpen={showEdit}
        onClose={() => setShowEdit(false)}
        title="Edit Task"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <Input
              value={editTask.title}
              onChange={(e) => setEditTask({ ...editTask, title: e.target.value })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={editTask.description}
              onChange={(e) => setEditTask({ ...editTask, description: e.target.value })}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Priority
              </label>
              <select
                value={editTask.priority}
                onChange={(e) => setEditTask({ ...editTask, priority: e.target.value })}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deadline
              </label>
              <Input
                type="date"
                value={editTask.deadline}
                onChange={(e) => setEditTask({ ...editTask, deadline: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setShowEdit(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>
              Save Changes
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
function KanbanColumn({ title, status, tasks, icon: Icon, color }) {
  const {
    setNodeRef,
    isOver,
  } = useDroppable({
    id: status,
  });

  const colorClasses = {
    blue: 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30',
    yellow: 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30',
    green: 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30'
  };

  return (
    <div className="flex-1 min-w-80">
      <div className={`${colorClasses[color]} border rounded-xl p-5 transition-all duration-200 ${isOver ? 'ring-2 ring-blue-400 ring-opacity-50' : ''}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
              color === 'blue' ? 'bg-blue-500' :
              color === 'yellow' ? 'bg-yellow-500' : 'bg-green-500'
            }`}>
              <Icon className="w-4 h-4 text-white" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
              {title}
            </h3>
          </div>
          <Badge
            variant="secondary"
            className={`text-sm font-medium ${
              color === 'blue' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300' :
              color === 'yellow' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
              'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
            }`}
          >
            {tasks.length}
          </Badge>
        </div>

        <div
          ref={setNodeRef}
          className="space-y-3 min-h-96"
        >
          <SortableContext items={tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
            {tasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
            {tasks.length === 0 && (
              <div className="flex items-center justify-center h-32 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg">
                <p className="text-gray-500 dark:text-gray-400 text-sm">
                  Drop tasks here or click "Add Task" to get started
                </p>
              </div>
            )}
          </SortableContext>
        </div>
      </div>
    </div>
  );
}
export default function KanbanBoard() {
  const { tasks, moveTask, currentProject, getTaskStats } = useTaskStore();
  const [activeId, setActiveId] = useState(null);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const projectTasks = tasks.filter(task => task.project === currentProject);
  const todoTasks = projectTasks.filter(task => task.status === 'todo');
  const inProgressTasks = projectTasks.filter(task => task.status === 'in-progress');
  const doneTasks = projectTasks.filter(task => task.status === 'done');
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };
  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeTask = projectTasks.find(task => task.id === active.id);
    if (!activeTask) return;

    // Get the container ID from the droppable area
    const overId = over.id;
    let newStatus = activeTask.status;

    // Determine new status based on drop target
    if (overId === 'todo') newStatus = 'todo';
    else if (overId === 'in-progress') newStatus = 'in-progress';
    else if (overId === 'done') newStatus = 'done';

    // Only update if status actually changed
    if (newStatus !== activeTask.status) {
      moveTask(active.id, newStatus);
      const statusNames = {
        'todo': 'To Do',
        'in-progress': 'In Progress',
        'done': 'Done'
      };
      toast.success(`Task moved to ${statusNames[newStatus]}`);
    }
  };
  const activeTask = activeId ? projectTasks.find(task => task.id === activeId) : null;

  const handleExportCSV = () => {
    const success = exportTasksToCSV(projectTasks, `${currentProject}-kanban-tasks`);
    if (success) {
      toast.success('Tasks exported to CSV successfully!');
    } else {
      toast.error('Failed to export tasks');
    }
  };

  const handleExportPDF = () => {
    const stats = getTaskStats();
    const success = exportTasksToPDF(projectTasks, stats, `${currentProject}-kanban-report`);
    if (success) {
      toast.success('Tasks exported to PDF successfully!');
    } else {
      toast.error('Failed to export tasks');
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Kanban Board
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Drag and drop tasks between columns to update their status
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleExportCSV}>
              Export CSV
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportPDF}>
              Export PDF
            </Button>
          </div>
        </div>
      </div>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-6 overflow-x-auto pb-4 kanban-board">
          <KanbanColumn
            title="To Do"
            status="todo"
            tasks={todoTasks}
            icon={CircleStackIcon}
            color="blue"
          />
          <KanbanColumn
            title="In Progress"
            status="in-progress"
            tasks={inProgressTasks}
            icon={PlayIcon}
            color="yellow"
          />
          <KanbanColumn
            title="Done"
            status="done"
            tasks={doneTasks}
            icon={CheckCircleIcon}
            color="green"
          />
        </div>
        <DragOverlay>
          {activeTask ? <TaskCard task={activeTask} /> : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
