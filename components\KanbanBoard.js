import { useState } from 'react';
import {
  Dnd<PERSON>ontext,
  DragOverlay,
  closestCorners,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTaskStore, useTimeTrackingStore } from '../lib/store';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { Modal } from './ui/Modal';
import { Input } from './ui/Input';
import { 
  Clock, 
  Calendar, 
  Edit, 
  Trash2, 
  Play, 
  Pause,
  MoreHorizontal 
} from 'lucide-react';
import { formatDate, getPriorityColor } from '../lib/utils';
import toast from 'react-hot-toast';
function TaskCard({ task }) {
  const { updateTask, deleteTask } = useTaskStore();
  const { activeTimer, startTimer, stopTimer } = useTimeTrackingStore();
  const [showEdit, setShowEdit] = useState(false);
  const [editTask, setEditTask] = useState(task);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const handleEdit = () => {
    updateTask(task.id, editTask);
    setShowEdit(false);
    toast.success('Task updated successfully');
  };
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      deleteTask(task.id);
      toast.success('Task deleted successfully');
    }
  };
  const handleTimer = () => {
    if (activeTimer?.taskId === task.id) {
      stopTimer();
      toast.success('Timer stopped');
    } else {
      startTimer(task.id, task.title);
      toast.success('Timer started');
    }
  };
  const isTimerActive = activeTimer?.taskId === task.id;
  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className={`mb-3 ${isDragging ? 'opacity-50' : ''}`}
      >
        <Card className="cursor-move hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <CardTitle className="text-sm font-medium">{task.title}</CardTitle>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTimer();
                  }}
                >
                  {isTimerActive ? (
                    <Pause className="h-3 w-3 text-red-500" />
                  ) : (
                    <Play className="h-3 w-3 text-green-500" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowEdit(true);
                  }}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-red-500 hover:text-red-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete();
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            {task.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                {task.description}
              </p>
            )}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant={task.priority} className="text-xs">
                  {task.priority}
                </Badge>
                {task.deadline && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(task.deadline)}
                  </div>
                )}
              </div>
              {isTimerActive && (
                <div className="flex items-center text-xs text-green-600">
                  <Clock className="h-3 w-3 mr-1" />
                  Active
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      <Modal
        isOpen={showEdit}
        onClose={() => setShowEdit(false)}
        title="Edit Task"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <Input
              value={editTask.title}
              onChange={(e) => setEditTask({ ...editTask, title: e.target.value })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={editTask.description}
              onChange={(e) => setEditTask({ ...editTask, description: e.target.value })}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Priority
              </label>
              <select
                value={editTask.priority}
                onChange={(e) => setEditTask({ ...editTask, priority: e.target.value })}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deadline
              </label>
              <Input
                type="date"
                value={editTask.deadline}
                onChange={(e) => setEditTask({ ...editTask, deadline: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setShowEdit(false)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>
              Save Changes
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
function KanbanColumn({ title, status, tasks }) {
  return (
    <div className="flex-1 min-w-80">
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          <Badge variant="secondary" className="text-xs">
            {tasks.length}
          </Badge>
        </div>
        <SortableContext items={tasks.map(task => task.id)} strategy={verticalListSortingStrategy}>
          <div className="space-y-2 min-h-96">
            {tasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        </SortableContext>
      </div>
    </div>
  );
}
export default function KanbanBoard() {
  const { tasks, moveTask, currentProject } = useTaskStore();
  const [activeId, setActiveId] = useState(null);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const projectTasks = tasks.filter(task => task.project === currentProject);
  const todoTasks = projectTasks.filter(task => task.status === 'todo');
  const inProgressTasks = projectTasks.filter(task => task.status === 'in-progress');
  const doneTasks = projectTasks.filter(task => task.status === 'done');
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };
  const handleDragEnd = (event) => {
    const { active, over } = event;
    setActiveId(null);
    if (!over) return;
    const activeTask = projectTasks.find(task => task.id === active.id);
    if (!activeTask) return;
    const overContainer = over.data?.current?.sortable?.containerId || over.id;
    let newStatus = activeTask.status;
    if (overContainer.includes('todo')) newStatus = 'todo';
    else if (overContainer.includes('in-progress')) newStatus = 'in-progress';
    else if (overContainer.includes('done')) newStatus = 'done';
    if (newStatus !== activeTask.status) {
      moveTask(active.id, newStatus);
      toast.success(`Task moved to ${newStatus.replace('-', ' ')}`);
    }
  };
  const activeTask = activeId ? projectTasks.find(task => task.id === activeId) : null;
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Kanban Board
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Drag and drop tasks between columns to update their status
        </p>
      </div>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-6 overflow-x-auto pb-4">
          <KanbanColumn
            title="To Do"
            status="todo"
            tasks={todoTasks}
          />
          <KanbanColumn
            title="In Progress"
            status="in-progress"
            tasks={inProgressTasks}
          />
          <KanbanColumn
            title="Done"
            status="done"
            tasks={doneTasks}
          />
        </div>
        <DragOverlay>
          {activeTask ? <TaskCard task={activeTask} /> : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
