# FocusFlow AI - Developer Guide

This guide provides detailed technical information for developers working on the FocusFlow AI project.

## 🏗 Architecture Overview

### Frontend Architecture
- **Framework**: Next.js 14 with React 18
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Zustand for global state
- **Component Structure**: Modular, reusable components following atomic design principles

### Backend Services
- **Authentication**: Firebase Auth with email/password
- **Database**: Firestore with real-time synchronization
- **AI Services**: OpenRouter API for multi-model AI access
- **File Storage**: Browser-based export functionality

### State Management Structure

#### Auth Store (`useAuthStore`)
```javascript
{
  user: User | null,           // Current authenticated user
  loading: boolean,            // Authentication loading state
  setUser: (user) => void,     // Set current user
  setLoading: (loading) => void, // Set loading state
  logout: () => void           // Clear user state
}
```

#### Task Store (`useTaskStore`)
```javascript
{
  tasks: Task[],               // Kanban board tasks
  todoTasks: TodoTask[],       // Quick todo items
  currentProject: string,      // Active project
  projects: string[],          // Available projects
  // Task management methods
  addTask, updateTask, deleteTask, moveTask,
  addTodoTask, updateTodoTask, deleteTodoTask,
  // Utility methods
  getTasksByStatus, getTaskStats
}
```

#### UI Store (`useUIStore`)
```javascript
{
  theme: 'light' | 'dark',     // Current theme
  sidebarCollapsed: boolean,   // Sidebar state
  showCalendar: boolean,       // Calendar view toggle
  showAnalytics: boolean,      // Analytics view toggle
  aiChatOpen: boolean,         // AI chat state
  notifications: boolean       // Notification preferences
}
```

#### Time Tracking Store (`useTimeTrackingStore`)
```javascript
{
  activeTimer: Timer | null,   // Currently running timer
  timeEntries: TimeEntry[],    // Historical time entries
  // Timer methods
  startTimer, stopTimer,
  // Analytics methods
  getTimeSpentOnTask, getTodayTimeEntries, getWeeklyStats
}
```

## 🔧 Component Architecture

### Core Components

#### 1. Authentication System
- `AuthWrapper.js`: Main authentication container
- `LoginForm.js`: User login interface
- `SignupForm.js`: User registration interface

#### 2. Dashboard Components
- `Sidebar.js`: Navigation and project management
- `KanbanBoard.js`: Drag-and-drop task management
- `TodoList.js`: Quick todo functionality
- `CalendarView.js`: Calendar integration with react-big-calendar
- `Analytics.js`: Productivity analytics dashboard
- `TimeTracker.js`: Time tracking interface

#### 3. AI Integration
- `AIAssistant.js`: Chat interface for AI interaction
- `VoiceCommands.js`: Voice recognition for task creation

#### 4. UI Components (`components/ui/`)
- `Button.js`: Reusable button component with variants
- `Card.js`: Container component for content sections
- `Input.js`: Form input component
- `Modal.js`: Modal dialog component
- `Badge.js`: Status and priority indicators

### Component Props and APIs

#### Task Object Structure
```javascript
{
  id: string,                  // Unique identifier
  title: string,               // Task title
  description?: string,        // Optional description
  status: 'todo' | 'in-progress' | 'done',
  priority: 'low' | 'medium' | 'high',
  deadline?: string,           // ISO date string
  project: string,             // Project identifier
  createdAt: string,           // ISO timestamp
  updatedAt?: string,          // ISO timestamp
  timeSpent?: number           // Milliseconds
}
```

#### TodoTask Object Structure
```javascript
{
  id: string,                  // Unique identifier
  title: string,               // Todo title
  completed: boolean,          // Completion status
  createdAt: string,           // ISO timestamp
  updatedAt?: string           // ISO timestamp
}
```

## 🤖 AI Integration

### OpenRouter Configuration
The AI system uses OpenRouter to access multiple language models:

```javascript
// Model selection based on task type
const AI_MODELS = {
  CLAUDE_4: 'anthropic/claude-3.5-sonnet',      // Creative tasks
  DEEPSEEK: 'deepseek/deepseek-chat',           // Analytical tasks
  GEMINI_PRO: 'google/gemini-pro-1.5',         // General assistance
  O3: 'openai/o3-mini',                        // Quick responses
  PROSEARCH: 'perplexity/llama-3.1-sonar-large-128k-online' // Research
};
```

### AI Assistant Commands
The AI assistant processes natural language commands through:

1. **Command Detection**: Analyze input to determine intent
2. **Model Selection**: Choose appropriate AI model based on task type
3. **Context Building**: Include current task state and user context
4. **Action Execution**: Perform requested actions (create, update, move tasks)
5. **Response Generation**: Provide user feedback and confirmations

### Supported Command Patterns
- Task Creation: `"Create task [title]"`, `"Add task [title] with [priority] priority"`
- Task Management: `"Move task [title] to [status]"`, `"Update task [title]"`
- Analysis: `"What should I focus on?"`, `"Analyze my productivity"`
- Scheduling: `"Schedule task [title] for [date]"`

## 🎨 Styling and Theming

### Tailwind Configuration
The project uses a custom Tailwind configuration with:
- Custom color palette for light/dark themes
- Responsive breakpoints for mobile-first design
- Custom animations and transitions
- Component-specific utility classes

### Theme System
Themes are managed through:
1. **CSS Variables**: Define color schemes in `globals.css`
2. **Tailwind Classes**: Use `dark:` prefix for dark mode styles
3. **JavaScript Toggle**: Theme switching through Zustand store
4. **Persistence**: Theme preference saved in localStorage

### Responsive Design Breakpoints
```css
sm: 640px    /* Mobile landscape */
md: 768px    /* Tablet */
lg: 1024px   /* Desktop */
xl: 1280px   /* Large desktop */
2xl: 1536px  /* Extra large */
```

## 🔒 Security Implementation

### Firebase Security Rules
```javascript
// Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Tasks are user-specific
    match /tasks/{taskId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
  }
}
```

### Data Validation
- Client-side validation using form validation
- Server-side validation through Firebase rules
- Input sanitization for XSS prevention
- API rate limiting through OpenRouter

## 🧪 Testing Strategy

### Recommended Testing Approach
1. **Unit Tests**: Test individual components and utilities
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Monitor bundle size and load times

### Testing Tools
- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Cypress**: End-to-end testing
- **Lighthouse**: Performance auditing

### Test Examples
```javascript
// Example component test
import { render, screen } from '@testing-library/react';
import { Button } from '../components/ui/Button';

test('renders button with correct text', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByText('Click me')).toBeInTheDocument();
});

// Example store test
import { useTaskStore } from '../lib/store';

test('adds task correctly', () => {
  const { addTask, tasks } = useTaskStore.getState();
  addTask({ title: 'Test Task', priority: 'high' });
  expect(tasks).toHaveLength(1);
  expect(tasks[0].title).toBe('Test Task');
});
```

## 🚀 Performance Optimization

### Code Splitting
- Dynamic imports for heavy components
- Route-based code splitting with Next.js
- Lazy loading for non-critical features

### State Management Optimization
- Zustand persistence for offline capability
- Selective re-renders with proper state slicing
- Memoization for expensive calculations

### Bundle Optimization
- Tree shaking for unused code elimination
- Image optimization with Next.js Image component
- CSS purging with Tailwind CSS

## 🔧 Development Workflow

### Local Development
```bash
# Start development server
npm run dev

# Run linting
npm run lint

# Build for production
npm run build

# Start production server
npm start
```

### Code Standards
- **ESLint**: Code linting with Next.js configuration
- **Prettier**: Code formatting (recommended)
- **Conventional Commits**: Commit message standards
- **Component Documentation**: JSDoc comments for complex components

### Git Workflow
1. Create feature branch from `main`
2. Implement feature with tests
3. Run linting and tests
4. Create pull request
5. Code review and merge

## 📊 Monitoring and Analytics

### Error Tracking
- Client-side error boundaries
- Console error logging
- User feedback collection

### Performance Monitoring
- Core Web Vitals tracking
- Bundle size monitoring
- API response time tracking

### User Analytics
- Task completion metrics
- Feature usage statistics
- User engagement tracking

## 🔄 Deployment Pipeline

### Environment Configuration
- **Development**: Local development with hot reload
- **Staging**: Preview deployments for testing
- **Production**: Optimized build with CDN

### CI/CD Pipeline
1. **Code Push**: Trigger automated pipeline
2. **Testing**: Run unit and integration tests
3. **Building**: Create optimized production build
4. **Deployment**: Deploy to hosting platform
5. **Monitoring**: Track deployment success

### Environment Variables
Ensure all required environment variables are set:
- Firebase configuration
- OpenRouter API key
- Site URL for CORS
- Analytics tracking IDs

## 🤝 Contributing Guidelines

### Code Style
- Use functional components with hooks
- Follow React best practices
- Implement proper error handling
- Write self-documenting code

### Component Guidelines
- Keep components focused and single-purpose
- Use TypeScript for complex components (optional)
- Implement proper prop validation
- Follow accessibility guidelines

### State Management
- Use appropriate store for different data types
- Implement proper error states
- Handle loading states consistently
- Persist important user preferences

This developer guide should help any programmer understand the project structure and be able to work on it immediately. For specific implementation details, refer to the individual component files and their inline documentation.
