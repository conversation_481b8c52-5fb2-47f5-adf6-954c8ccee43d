"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanBoard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @dnd-kit/utilities */ \"./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Edit,MoreHorizontal,Pause,Play,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Clock,Edit,MoreHorizontal,Pause,Play,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _utils_exportData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../utils/exportData */ \"./utils/exportData.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TaskCard(param) {\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const { activeTimer, startTimer, stopTimer } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore)();\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTask, setEditTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(task);\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__.CSS.Transform.toString(transform),\n        transition\n    };\n    const handleEdit = ()=>{\n        updateTask(task.id, editTask);\n        setShowEdit(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task updated successfully\");\n    };\n    const handleDelete = ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            deleteTask(task.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task deleted successfully\");\n        }\n    };\n    const handleTimer = ()=>{\n        if ((activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id) {\n            stopTimer();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer stopped\");\n        } else {\n            startTimer(task.id, task.title);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer started\");\n        }\n    };\n    const isTimerActive = (activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"mb-3 \".concat(isDragging ? \"opacity-50\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"cursor-move hover:shadow-md transition-shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"pb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: task.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleTimer();\n                                                },\n                                                children: isTimerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Pause, {\n                                                    className: \"h-3 w-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Play, {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Edit, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Trash2, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"pt-0\",\n                            children: [\n                                task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400 mb-2\",\n                                    children: task.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: task.priority,\n                                                    className: \"text-xs\",\n                                                    children: task.priority\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                task.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Calendar, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(task.deadline)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTimerActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Clock, {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false),\n                title: \"Edit Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                    value: editTask.title,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            title: e.target.value\n                                        })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: editTask.description,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: editTask.priority,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                            type: \"date\",\n                                            value: editTask.deadline,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowEdit(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    onClick: handleEdit,\n                                    children: \"Save Changes\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TaskCard, \"phrOKTed3xFlYlOxG+d3h1gAb1k=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore,\n        _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable\n    ];\n});\n_c = TaskCard;\nfunction KanbanColumn(param) {\n    let { title, status, tasks, icon: Icon, color } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable)({\n        id: status\n    });\n    const colorClasses = {\n        blue: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n        yellow: \"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30\",\n        green: \"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-80\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(colorClasses[color], \" border rounded-xl p-5 transition-all duration-200 \").concat(isOver ? \"ring-2 ring-blue-400 ring-opacity-50\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center \".concat(color === \"blue\" ? \"bg-blue-500\" : color === \"yellow\" ? \"bg-yellow-500\" : \"bg-green-500\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white text-lg\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-sm font-medium \".concat(color === \"blue\" ? \"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\" : color === \"yellow\" ? \"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300\" : \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\"),\n                            children: tasks.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: setNodeRef,\n                    className: \"space-y-3 min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                        children: [\n                            tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                                    task: task\n                                }, task.id, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)),\n                            tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                    children: 'Drop tasks here or click \"Add Task\" to get started'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s1(KanbanColumn, \"B0t40knSjPgPvDEIZEaFzamr7vc=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable\n    ];\n});\n_c1 = KanbanColumn;\nfunction KanbanBoard() {\n    _s2();\n    const { tasks, moveTask, currentProject, getTaskStats } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const projectTasks = tasks.filter((task)=>task.project === currentProject);\n    const todoTasks = projectTasks.filter((task)=>task.status === \"todo\");\n    const inProgressTasks = projectTasks.filter((task)=>task.status === \"in-progress\");\n    const doneTasks = projectTasks.filter((task)=>task.status === \"done\");\n    const handleDragStart = (event)=>{\n        setActiveId(event.active.id);\n    };\n    const handleDragEnd = (event)=>{\n        var _over_data_current_sortable, _over_data_current, _over_data;\n        const { active, over } = event;\n        setActiveId(null);\n        if (!over) return;\n        const activeTask = projectTasks.find((task)=>task.id === active.id);\n        if (!activeTask) return;\n        const overContainer = ((_over_data = over.data) === null || _over_data === void 0 ? void 0 : (_over_data_current = _over_data.current) === null || _over_data_current === void 0 ? void 0 : (_over_data_current_sortable = _over_data_current.sortable) === null || _over_data_current_sortable === void 0 ? void 0 : _over_data_current_sortable.containerId) || over.id;\n        let newStatus = activeTask.status;\n        if (overContainer.includes(\"todo\")) newStatus = \"todo\";\n        else if (overContainer.includes(\"in-progress\")) newStatus = \"in-progress\";\n        else if (overContainer.includes(\"done\")) newStatus = \"done\";\n        if (newStatus !== activeTask.status) {\n            moveTask(active.id, newStatus);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task moved to \".concat(newStatus.replace(\"-\", \" \")));\n        }\n    };\n    const activeTask = activeId ? projectTasks.find((task)=>task.id === activeId) : null;\n    const handleExportCSV = ()=>{\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToCSV)(projectTasks, \"\".concat(currentProject, \"-kanban-tasks\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to CSV successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    const handleExportPDF = ()=>{\n        const stats = getTaskStats();\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToPDF)(projectTasks, stats, \"\".concat(currentProject, \"-kanban-report\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to PDF successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Kanban Board\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Drag and drop tasks between columns to update their status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportCSV,\n                                    children: \"Export CSV\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportPDF,\n                                    children: \"Export PDF\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 overflow-x-auto pb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"To Do\",\n                                status: \"todo\",\n                                tasks: todoTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"In Progress\",\n                                status: \"in-progress\",\n                                tasks: inProgressTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"Done\",\n                                status: \"done\",\n                                tasks: doneTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                            task: activeTask\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 385,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, this);\n}\n_s2(KanbanBoard, \"dKIcBJsvhRbKqNVhbc0ByDX8JMo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors\n    ];\n});\n_c2 = KanbanBoard;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TaskCard\");\n$RefreshReg$(_c1, \"KanbanColumn\");\n$RefreshReg$(_c2, \"KanbanBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n"));

/***/ })

});