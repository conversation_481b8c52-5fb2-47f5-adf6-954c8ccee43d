/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./lib/firebase-config.js":
/*!********************************!*\
  !*** ./lib/firebase-config.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"firebase/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_auth__WEBPACK_IMPORTED_MODULE_1__, firebase_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAvL0q2Bz4ZLNPVGJjo2gNMEDddra87odQ\",\n    authDomain: \"zatconss.firebaseapp.com\",\n    projectId: \"zatconss\",\n    storageBucket: \"zatconss.firebasestorage.app\",\n    messagingSenderId: \"947257597349\",\n    appId: \"1:947257597349:web:4f62c8e2bf4952eebe5c4c\"\n};\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/firebase-config.js\n");

/***/ }),

/***/ "./lib/store.js":
/*!**********************!*\
  !*** ./lib/store.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useTaskStore: () => (/* binding */ useTaskStore),\n/* harmony export */   useTimeTrackingStore: () => (/* binding */ useTimeTrackingStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"zustand/middleware\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        user: null,\n        loading: true,\n        setUser: (user)=>set({\n                user,\n                loading: false\n            }),\n        setLoading: (loading)=>set({\n                loading\n            }),\n        logout: ()=>set({\n                user: null,\n                loading: false\n            })\n    }));\nconst useTaskStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        tasks: [],\n        todoTasks: [],\n        currentProject: \"default\",\n        projects: [\n            \"default\"\n        ],\n        addTask: (task)=>set((state)=>({\n                    tasks: [\n                        ...state.tasks,\n                        {\n                            ...task,\n                            id: Date.now().toString(),\n                            createdAt: new Date().toISOString()\n                        }\n                    ]\n                })),\n        updateTask: (taskId, updates)=>set((state)=>({\n                    tasks: state.tasks.map((task)=>task.id === taskId ? {\n                            ...task,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : task)\n                })),\n        deleteTask: (taskId)=>set((state)=>({\n                    tasks: state.tasks.filter((task)=>task.id !== taskId)\n                })),\n        moveTask: (taskId, newStatus)=>set((state)=>({\n                    tasks: state.tasks.map((task)=>task.id === taskId ? {\n                            ...task,\n                            status: newStatus,\n                            updatedAt: new Date().toISOString()\n                        } : task)\n                })),\n        addTodoTask: (task)=>set((state)=>({\n                    todoTasks: [\n                        ...state.todoTasks,\n                        {\n                            ...task,\n                            id: Date.now().toString(),\n                            completed: false,\n                            createdAt: new Date().toISOString()\n                        }\n                    ]\n                })),\n        updateTodoTask: (taskId, updates)=>set((state)=>({\n                    todoTasks: state.todoTasks.map((task)=>task.id === taskId ? {\n                            ...task,\n                            ...updates,\n                            updatedAt: new Date().toISOString()\n                        } : task)\n                })),\n        deleteTodoTask: (taskId)=>set((state)=>({\n                    todoTasks: state.todoTasks.filter((task)=>task.id !== taskId)\n                })),\n        reorderTodoTasks: (newOrder)=>set({\n                todoTasks: newOrder\n            }),\n        setCurrentProject: (project)=>set({\n                currentProject: project\n            }),\n        addProject: (project)=>set((state)=>({\n                    projects: [\n                        ...state.projects,\n                        project\n                    ]\n                })),\n        getTasksByStatus: (status)=>{\n            const { tasks, currentProject } = get();\n            return tasks.filter((task)=>task.status === status && task.project === currentProject);\n        },\n        getTaskStats: ()=>{\n            const { tasks, todoTasks, currentProject } = get();\n            const projectTasks = tasks.filter((task)=>task.project === currentProject);\n            const completedTasks = projectTasks.filter((task)=>task.status === \"done\").length;\n            const inProgressTasks = projectTasks.filter((task)=>task.status === \"in-progress\").length;\n            const todoTasksCount = projectTasks.filter((task)=>task.status === \"todo\").length;\n            const completedTodoTasks = todoTasks.filter((task)=>task.completed).length;\n            return {\n                total: projectTasks.length + todoTasks.length,\n                completed: completedTasks + completedTodoTasks,\n                inProgress: inProgressTasks,\n                todo: todoTasksCount + todoTasks.filter((task)=>!task.completed).length,\n                completionRate: projectTasks.length > 0 ? Math.round(completedTasks / projectTasks.length * 100) : 0\n            };\n        }\n    }), {\n    name: \"focusflow-tasks\",\n    partialize: (state)=>({\n            tasks: state.tasks,\n            todoTasks: state.todoTasks,\n            currentProject: state.currentProject,\n            projects: state.projects\n        })\n}));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        theme: \"light\",\n        sidebarCollapsed: false,\n        showCalendar: false,\n        showAnalytics: false,\n        aiChatOpen: false,\n        notifications: true,\n        toggleTheme: ()=>set((state)=>({\n                    theme: state.theme === \"light\" ? \"dark\" : \"light\"\n                })),\n        setSidebarCollapsed: (collapsed)=>set({\n                sidebarCollapsed: collapsed\n            }),\n        setShowCalendar: (show)=>set({\n                showCalendar: show\n            }),\n        setShowAnalytics: (show)=>set({\n                showAnalytics: show\n            }),\n        setAiChatOpen: (open)=>set({\n                aiChatOpen: open\n            }),\n        setNotifications: (enabled)=>set({\n                notifications: enabled\n            })\n    }), {\n    name: \"focusflow-ui\",\n    partialize: (state)=>({\n            theme: state.theme,\n            sidebarCollapsed: state.sidebarCollapsed,\n            notifications: state.notifications\n        })\n}));\nconst useTimeTrackingStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        activeTimer: null,\n        timeEntries: [],\n        startTimer: (taskId, taskTitle)=>{\n            const { activeTimer } = get();\n            if (activeTimer) {\n                get().stopTimer();\n            }\n            set({\n                activeTimer: {\n                    taskId,\n                    taskTitle,\n                    startTime: Date.now()\n                }\n            });\n        },\n        stopTimer: ()=>{\n            const { activeTimer } = get();\n            if (!activeTimer) return;\n            const endTime = Date.now();\n            const duration = endTime - activeTimer.startTime;\n            set((state)=>({\n                    activeTimer: null,\n                    timeEntries: [\n                        ...state.timeEntries,\n                        {\n                            id: Date.now().toString(),\n                            taskId: activeTimer.taskId,\n                            taskTitle: activeTimer.taskTitle,\n                            startTime: activeTimer.startTime,\n                            endTime,\n                            duration,\n                            date: new Date().toISOString().split(\"T\")[0]\n                        }\n                    ]\n                }));\n            return duration;\n        },\n        getTimeSpentOnTask: (taskId)=>{\n            const { timeEntries } = get();\n            return timeEntries.filter((entry)=>entry.taskId === taskId).reduce((total, entry)=>total + entry.duration, 0);\n        },\n        getTodayTimeEntries: ()=>{\n            const { timeEntries } = get();\n            const today = new Date().toISOString().split(\"T\")[0];\n            return timeEntries.filter((entry)=>entry.date === today);\n        },\n        getWeeklyStats: ()=>{\n            const { timeEntries } = get();\n            const weekAgo = new Date();\n            weekAgo.setDate(weekAgo.getDate() - 7);\n            const weeklyEntries = timeEntries.filter((entry)=>new Date(entry.date) >= weekAgo);\n            const totalTime = weeklyEntries.reduce((total, entry)=>total + entry.duration, 0);\n            const dailyStats = {};\n            weeklyEntries.forEach((entry)=>{\n                if (!dailyStats[entry.date]) {\n                    dailyStats[entry.date] = 0;\n                }\n                dailyStats[entry.date] += entry.duration;\n            });\n            return {\n                totalTime,\n                dailyStats,\n                averageDaily: totalTime / 7\n            };\n        }\n    }), {\n    name: \"focusflow-time-tracking\",\n    partialize: (state)=>({\n            timeEntries: state.timeEntries\n        })\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/store.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"firebase/auth\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([firebase_auth__WEBPACK_IMPORTED_MODULE_2__, _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__, _lib_store__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const { setUser, setLoading } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { theme } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useUIStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth, (user)=>{\n            setUser(user);\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, [\n        setUser,\n        setLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (theme === \"dark\") {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n    }, [\n        theme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: theme === \"dark\" ? \"#374151\" : \"#ffffff\",\n                        color: theme === \"dark\" ? \"#f3f4f6\" : \"#111827\"\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\pages\\\\_app.js\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "firebase/app":
/*!*******************************!*\
  !*** external "firebase/app" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/app");;

/***/ }),

/***/ "firebase/auth":
/*!********************************!*\
  !*** external "firebase/auth" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/auth");;

/***/ }),

/***/ "firebase/firestore":
/*!*************************************!*\
  !*** external "firebase/firestore" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase/firestore");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "zustand/middleware":
/*!*************************************!*\
  !*** external "zustand/middleware" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand/middleware");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();