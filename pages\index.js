import Head from 'next/head';
import { useAuthStore } from '../lib/store';

export default function Home() {
  const { user, loading } = useAuthStore();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading FocusFlow AI...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Welcome to FocusFlow AI
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Please sign in to continue
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Head>
        <title>FocusFlow AI - Your Intelligent Productivity Companion</title>
        <meta name="description" content="Boost your productivity with AI-powered task management, smart scheduling, and intelligent insights." />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            FocusFlow AI
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
            Your Intelligent Productivity Companion
          </p>
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              Welcome! The application is loading...
            </p>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
