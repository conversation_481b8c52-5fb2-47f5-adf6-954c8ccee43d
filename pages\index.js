import { useState } from 'react';
import Head from 'next/head';
import { useAuthStore, useUIStore } from '../lib/store';
import { Menu, X } from 'lucide-react';
import AuthWrapper from '../components/Auth/AuthWrapper';
import Sidebar from '../components/Sidebar';
import KanbanBoard from '../components/KanbanBoard';
import TodoList from '../components/TodoList';
import CalendarView from '../components/CalendarView';
import Analytics from '../components/Analytics';
import AIAssistant from '../components/AIAssistant';

export default function Home() {
  const { user, loading } = useAuthStore();
  const { showCalendar, showAnalytics } = useUIStore();
  const [activeView, setActiveView] = useState('kanban');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading FocusFlow AI...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <AuthWrapper />;
  }

  const renderMainContent = () => {
    if (showCalendar) {
      return <CalendarView />;
    }
    if (showAnalytics) {
      return <Analytics />;
    }
    if (activeView === 'todo') {
      return <TodoList />;
    }
    return <KanbanBoard />;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Head>
        <title>FocusFlow AI - Your Intelligent Productivity Companion</title>
        <meta name="description" content="Boost your productivity with AI-powered task management, smart scheduling, and intelligent insights." />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="flex h-screen">
        <div className={`sidebar-mobile ${sidebarOpen ? '' : 'closed'} lg:translate-x-0`}>
          <Sidebar />
        </div>
        {sidebarOpen && (
          <div
            className="mobile-overlay"
            onClick={() => setSidebarOpen(false)}
          />
        )}
        <main className="main-content">
          <div className="mobile-nav">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              FocusFlow AI
            </h1>
            <div></div>
          </div>
          <div className="p-4">
            <div className="flex items-center justify-between mb-6">
              <div className="flex space-x-2 overflow-x-auto">
                <button
                  onClick={() => setActiveView('kanban')}
                  className={`px-3 py-2 rounded-lg font-medium transition-colors whitespace-nowrap text-sm ${
                    activeView === 'kanban' && !showCalendar && !showAnalytics
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Kanban Board
                </button>
                <button
                  onClick={() => setActiveView('todo')}
                  className={`px-3 py-2 rounded-lg font-medium transition-colors whitespace-nowrap text-sm ${
                    activeView === 'todo' && !showCalendar && !showAnalytics
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Quick Todos
                </button>
              </div>
            </div>
            {renderMainContent()}
          </div>
        </main>
      </div>
      <AIAssistant />
    </div>
  );
}
