import { useState } from 'react';
import Head from 'next/head';
import { useAuthStore, useUIStore } from '../lib/store';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import AuthWrapper from '../components/Auth/AuthWrapper';
import Sidebar from '../components/Sidebar';
import KanbanBoard from '../components/KanbanBoard';
import TodoList from '../components/TodoList';
import CalendarView from '../components/CalendarView';
import Analytics from '../components/Analytics';
import AIAssistant from '../components/AIAssistant';

export default function Home() {
  const { user, loading } = useAuthStore();
  const { showCalendar, showAnalytics } = useUIStore();
  const [activeView, setActiveView] = useState('kanban');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading FocusFlow AI...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <AuthWrapper />;
  }

  const renderMainContent = () => {
    if (showCalendar) {
      return <CalendarView />;
    }
    if (showAnalytics) {
      return <Analytics />;
    }
    if (activeView === 'todo') {
      return <TodoList />;
    }
    return <KanbanBoard />;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Head>
        <title>FocusFlow AI - Your Intelligent Productivity Companion</title>
        <meta name="description" content="Boost your productivity with AI-powered task management, smart scheduling, and intelligent insights." />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <div className={`sidebar-mobile ${sidebarOpen ? '' : 'closed'} lg:translate-x-0`}>
          <Sidebar />
        </div>
        {sidebarOpen && (
          <div
            className="mobile-overlay"
            onClick={() => setSidebarOpen(false)}
          />
        )}
        <main className="main-content bg-white dark:bg-gray-900">
          <div className="mobile-nav bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              {sidebarOpen ? <XMarkIcon className="w-5 h-5" /> : <Bars3Icon className="w-5 h-5" />}
            </button>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              FocusFlow AI
            </h1>
            <div></div>
          </div>
          <div className="p-8">
            <div className="flex items-center justify-between mb-8">
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-xl">
                <button
                  onClick={() => setActiveView('kanban')}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap text-sm ${
                    activeView === 'kanban' && !showCalendar && !showAnalytics
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  📋 Kanban Board
                </button>
                <button
                  onClick={() => setActiveView('todo')}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap text-sm ${
                    activeView === 'todo' && !showCalendar && !showAnalytics
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  ✅ Quick Todos
                </button>
              </div>
            </div>
            <div className="max-w-7xl mx-auto">
              {renderMainContent()}
            </div>
          </div>
        </main>
      </div>
      <AIAssistant />
    </div>
  );
}
