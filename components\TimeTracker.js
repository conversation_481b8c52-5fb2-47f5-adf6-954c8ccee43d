import { useState, useEffect } from 'react';
import { useTimeTrackingStore, useTaskStore } from '../lib/store';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  Calendar,
  TrendingUp,
  Target
} from 'lucide-react';
import { formatTime } from '../lib/utils';
import toast from 'react-hot-toast';
function ActiveTimer() {
  const { activeTimer, stopTimer } = useTimeTrackingStore();
  const [elapsedTime, setElapsedTime] = useState(0);
  useEffect(() => {
    if (!activeTimer) {
      setElapsedTime(0);
      return;
    }
    const interval = setInterval(() => {
      setElapsedTime(Date.now() - activeTimer.startTime);
    }, 1000);
    return () => clearInterval(interval);
  }, [activeTimer]);
  const handleStop = () => {
    const duration = stopTimer();
    if (duration) {
      toast.success(`Timer stopped. Total time: ${formatTime(duration)}`);
    }
  };
  if (!activeTimer) return null;
  return (
    <Card className="border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <CardTitle className="text-sm text-green-800 dark:text-green-200">
              Active Timer
            </CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleStop}
            className="text-green-700 border-green-300 hover:bg-green-100"
          >
            <Square className="w-3 h-3 mr-1" />
            Stop
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <p className="font-medium text-green-900 dark:text-green-100">
            {activeTimer.taskTitle}
          </p>
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-green-600" />
            <span className="text-lg font-mono text-green-800 dark:text-green-200">
              {formatTime(elapsedTime)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
function TimeEntries() {
  const { getTodayTimeEntries } = useTimeTrackingStore();
  const todayEntries = getTodayTimeEntries();
  const totalTime = todayEntries.reduce((sum, entry) => sum + entry.duration, 0);
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="w-4 h-4" />
          <span>Today's Time Entries</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {todayEntries.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            No time entries for today
          </p>
        ) : (
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <span className="font-medium text-blue-900 dark:text-blue-100">
                Total Time Today
              </span>
              <span className="font-mono text-lg text-blue-800 dark:text-blue-200">
                {formatTime(totalTime)}
              </span>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {todayEntries.map((entry) => (
                <div
                  key={entry.id}
                  className="flex justify-between items-center p-2 border border-gray-200 dark:border-gray-700 rounded"
                >
                  <div>
                    <p className="font-medium text-sm">{entry.taskTitle}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(entry.startTime).toLocaleTimeString()} - {new Date(entry.endTime).toLocaleTimeString()}
                    </p>
                  </div>
                  <Badge variant="secondary" className="font-mono text-xs">
                    {formatTime(entry.duration)}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
function WeeklyStats() {
  const { getWeeklyStats } = useTimeTrackingStore();
  const weeklyStats = getWeeklyStats();
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const maxDailyTime = Math.max(...Object.values(weeklyStats.dailyStats));
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="w-4 h-4" />
          <span>Weekly Overview</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Week</p>
              <p className="font-mono text-lg font-semibold">
                {formatTime(weeklyStats.totalTime)}
              </p>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Daily Average</p>
              <p className="font-mono text-lg font-semibold">
                {formatTime(weeklyStats.averageDaily)}
              </p>
            </div>
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Daily Breakdown
            </p>
            <div className="space-y-1">
              {days.map((day, index) => {
                const date = new Date();
                date.setDate(date.getDate() - (6 - index));
                const dateKey = date.toISOString().split('T')[0];
                const dayTime = weeklyStats.dailyStats[dateKey] || 0;
                const percentage = maxDailyTime > 0 ? (dayTime / maxDailyTime) * 100 : 0;
                return (
                  <div key={day} className="flex items-center space-x-2">
                    <span className="text-xs w-8 text-gray-600 dark:text-gray-400">
                      {day}
                    </span>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-xs font-mono w-12 text-right">
                      {dayTime > 0 ? formatTime(dayTime) : '0m'}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
function TaskTimeStats() {
  const { timeEntries, getTimeSpentOnTask } = useTimeTrackingStore();
  const { tasks, currentProject } = useTaskStore();
  const projectTasks = tasks.filter(task => task.project === currentProject);
  const tasksWithTime = projectTasks
    .map(task => ({
      ...task,
      timeSpent: getTimeSpentOnTask(task.id)
    }))
    .filter(task => task.timeSpent > 0)
    .sort((a, b) => b.timeSpent - a.timeSpent)
    .slice(0, 5);
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="w-4 h-4" />
          <span>Top Tasks by Time</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {tasksWithTime.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            No time tracked for tasks yet
          </p>
        ) : (
          <div className="space-y-3">
            {tasksWithTime.map((task) => (
              <div
                key={task.id}
                className="flex justify-between items-center p-2 border border-gray-200 dark:border-gray-700 rounded"
              >
                <div className="flex-1">
                  <p className="font-medium text-sm">{task.title}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant={task.priority} className="text-xs">
                      {task.priority}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {task.status.replace('-', ' ')}
                    </Badge>
                  </div>
                </div>
                <Badge variant="secondary" className="font-mono">
                  {formatTime(task.timeSpent)}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
export default function TimeTracker() {
  return (
    <div className="p-6 space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Time Tracking
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Monitor your productivity and track time spent on tasks
        </p>
      </div>
      <ActiveTimer />
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TimeEntries />
        <WeeklyStats />
      </div>
      <TaskTimeStats />
    </div>
  );
}
