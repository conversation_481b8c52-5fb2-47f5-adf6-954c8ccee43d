"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _VoiceCommands__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./VoiceCommands */ \"./components/VoiceCommands.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckCircle2,ChevronDown,Circle,Clock,FolderOpen,LogOut,MessageCircle,Moon,PlayCircle,Plus,Settings,Sun,Target,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,CheckCircle2,ChevronDown,Circle,Clock,FolderOpen,LogOut,MessageCircle,Moon,PlayCircle,Plus,Settings,Sun,Target,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { getTaskStats, currentProject, projects, setCurrentProject, addProject } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const { theme, toggleTheme, setShowCalendar, setShowAnalytics, setAiChatOpen } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useUIStore)();\n    const [showAddTask, setShowAddTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewProject, setShowNewProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProjectName, setNewProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTask, setNewTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        priority: \"medium\",\n        deadline: \"\"\n    });\n    const stats = getTaskStats();\n    const handleLogout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Logged out successfully\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Failed to logout\");\n        }\n    };\n    const handleAddTask = ()=>{\n        if (!newTask.title.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Please enter a task title\");\n            return;\n        }\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore.getState().addTask({\n            ...newTask,\n            status: \"todo\",\n            project: currentProject\n        });\n        setNewTask({\n            title: \"\",\n            description: \"\",\n            priority: \"medium\",\n            deadline: \"\"\n        });\n        setShowAddTask(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Task added successfully\");\n    };\n    const handleAddProject = ()=>{\n        if (!newProjectName.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Please enter a project name\");\n            return;\n        }\n        if (projects.includes(newProjectName)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Project already exists\");\n            return;\n        }\n        addProject(newProjectName);\n        setCurrentProject(newProjectName);\n        setNewProjectName(\"\");\n        setShowNewProject(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Project created successfully\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-white dark:bg-gray-900 border-r border-gray-100 dark:border-gray-800 flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-100 dark:border-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Zap, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"FocusFlow AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: \"Productivity Companion\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: toggleTheme,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\",\n                                        children: theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Moon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 36\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Sun, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.User, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white truncate\",\n                                                        children: (user === null || user === void 0 ? void 0 : user.displayName) || \"Welcome User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 truncate\",\n                                                        children: user === null || user === void 0 ? void 0 : user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                        children: \"Current Project\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowNewProject(true),\n                                        className: \"h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Plus, {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: currentProject,\n                                        onChange: (e)=>setCurrentProject(e.target.value),\n                                        className: \"w-full p-3 pr-8 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer\",\n                                        children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: project,\n                                                children: project === \"default\" ? \"\\uD83D\\uDCCB Personal Workspace\" : \"\\uD83D\\uDCC1 \".concat(project)\n                                            }, project, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.ChevronDown, {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                children: \"Task Overview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Total\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: stats.completed\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-yellow-600\",\n                                                children: stats.inProgress\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"To Do\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: stats.todo\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowAddTask(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"default\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Plus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Task\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setAiChatOpen(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.MessageCircle, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Talk to Assistant\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowCalendar(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"ghost\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Calendar, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Calendar View\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowAnalytics(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"ghost\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.BarChart3, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Analytics\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VoiceCommands__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: toggleTheme,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Moon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 36\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Sun, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 67\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleLogout,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.LogOut, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                isOpen: showAddTask,\n                onClose: ()=>setShowAddTask(false),\n                title: \"Add New Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                    value: newTask.title,\n                                    onChange: (e)=>setNewTask({\n                                            ...newTask,\n                                            title: e.target.value\n                                        }),\n                                    placeholder: \"Enter task title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTask.description,\n                                    onChange: (e)=>setNewTask({\n                                            ...newTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3,\n                                    placeholder: \"Enter task description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newTask.priority,\n                                            onChange: (e)=>setNewTask({\n                                                    ...newTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                            type: \"date\",\n                                            value: newTask.deadline,\n                                            onChange: (e)=>setNewTask({\n                                                    ...newTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowAddTask(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleAddTask,\n                                    children: \"Add Task\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                isOpen: showNewProject,\n                onClose: ()=>setShowNewProject(false),\n                title: \"Create New Project\",\n                size: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Project Name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                    value: newProjectName,\n                                    onChange: (e)=>setNewProjectName(e.target.value),\n                                    placeholder: \"Enter project name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewProject(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleAddProject,\n                                    children: \"Create Project\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"K9ayOq3O4A6NWMvNVNHI1a4q3no=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useUIStore\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ })

});