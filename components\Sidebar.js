import { useState } from 'react';
import { signOut } from 'firebase/auth';
import { auth } from '../lib/firebase-config';
import { useAuthStore, useTaskStore, useUIStore } from '../lib/store';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Modal } from './ui/Modal';
import { Input } from './ui/Input';
import VoiceCommands from './VoiceCommands';
import {
  Plus,
  MessageCircle,
  LogOut,
  Moon,
  Sun,
  Calendar,
  BarChart3,
  FolderOpen,
  User,
  Settings,
  ChevronDown,
  Target,
  Clock,
  CheckCircle2,
  Circle,
  PlayCircle,
  Zap
} from 'lucide-react';
import toast from 'react-hot-toast';
export default function Sidebar() {
  const { user } = useAuthStore();
  const { getTaskStats, currentProject, projects, setCurrentProject, addProject } = useTaskStore();
  const { theme, toggleTheme, setShowCalendar, setShowAnalytics, setAiChatOpen } = useUIStore();
  const [showAddTask, setShowAddTask] = useState(false);
  const [showNewProject, setShowNewProject] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium',
    deadline: ''
  });
  const stats = getTaskStats();
  const handleLogout = async () => {
    try {
      await signOut(auth);
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };
  const handleAddTask = () => {
    if (!newTask.title.trim()) {
      toast.error('Please enter a task title');
      return;
    }
    useTaskStore.getState().addTask({
      ...newTask,
      status: 'todo',
      project: currentProject
    });
    setNewTask({ title: '', description: '', priority: 'medium', deadline: '' });
    setShowAddTask(false);
    toast.success('Task added successfully');
  };
  const handleAddProject = () => {
    if (!newProjectName.trim()) {
      toast.error('Please enter a project name');
      return;
    }
    if (projects.includes(newProjectName)) {
      toast.error('Project already exists');
      return;
    }
    addProject(newProjectName);
    setCurrentProject(newProjectName);
    setNewProjectName('');
    setShowNewProject(false);
    toast.success('Project created successfully');
  };
  return (
    <>
      <div className="w-80 bg-white dark:bg-gray-900 border-r border-gray-100 dark:border-gray-800 flex flex-col h-full">
        {/* Header Section */}
        <div className="p-6 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                <Zap className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">FocusFlow AI</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Productivity Companion</p>
              </div>
            </div>
            <Button
              onClick={toggleTheme}
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {theme === 'light' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
            </Button>
          </div>

          {/* User Profile Card */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 dark:text-white truncate">
                    {user?.displayName || 'Welcome User'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {user?.email}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Project Selector */}
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Current Project</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNewProject(true)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>
          <div className="relative">
            <select
              value={currentProject}
              onChange={(e) => setCurrentProject(e.target.value)}
              className="w-full p-3 pr-8 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm font-medium focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer"
            >
              {projects.map(project => (
                <option key={project} value={project}>
                  {project === 'default' ? '📋 Personal Workspace' : `📁 ${project}`}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
        {/* Task Overview */}
        <div className="px-6 py-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Task Overview</h4>
          <div className="grid grid-cols-2 gap-3 mb-4">
            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <div>
                    <p className="text-lg font-semibold text-green-700 dark:text-green-300">{stats.completed}</p>
                    <p className="text-xs text-green-600 dark:text-green-400">Completed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2">
                  <Circle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <div>
                    <p className="text-lg font-semibold text-blue-700 dark:text-blue-300">{stats.todo}</p>
                    <p className="text-xs text-blue-600 dark:text-blue-400">To Do</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2">
                  <PlayCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <div>
                    <p className="text-lg font-semibold text-yellow-700 dark:text-yellow-300">{stats.inProgress}</p>
                    <p className="text-xs text-yellow-600 dark:text-yellow-400">In Progress</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20 border-gray-100 dark:border-gray-900/30">
              <CardContent className="p-3">
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  <div>
                    <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">{stats.total}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Total</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress Bar */}
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Completion Rate</span>
              <span className="text-xs font-semibold text-gray-900 dark:text-white">{stats.completionRate}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${stats.completionRate}%` }}
              />
            </div>
          </div>
        </div>
        {/* Quick Actions */}
        <div className="flex-1 px-6 py-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Quick Actions</h4>
          <div className="space-y-2 mb-6">
            <Button
              onClick={() => setShowAddTask(true)}
              className="w-full justify-start h-11 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-sm"
              size="default"
            >
              <Plus className="w-4 h-4 mr-3" />
              <span className="font-medium">Add New Task</span>
            </Button>

            <Button
              onClick={() => setAiChatOpen(true)}
              className="w-full justify-start h-10 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/20 dark:to-indigo-950/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800 hover:bg-gradient-to-r hover:from-purple-100 hover:to-indigo-100 dark:hover:from-purple-900/30 dark:hover:to-indigo-900/30"
              variant="outline"
            >
              <MessageCircle className="w-4 h-4 mr-3" />
              <span className="font-medium">AI Assistant</span>
            </Button>

            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => setShowCalendar(true)}
                className="justify-center h-10 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                variant="ghost"
                size="sm"
              >
                <Calendar className="w-4 h-4 mr-2" />
                <span className="text-sm">Calendar</span>
              </Button>

              <Button
                onClick={() => setShowAnalytics(true)}
                className="justify-center h-10 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                variant="ghost"
                size="sm"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                <span className="text-sm">Analytics</span>
              </Button>
            </div>
          </div>

          {/* Voice Commands Section */}
          <div className="mb-6">
            <VoiceCommands />
          </div>
        </div>
        {/* Footer */}
        <div className="p-6 border-t border-gray-100 dark:border-gray-800 mt-auto">
          <Button
            onClick={handleLogout}
            variant="ghost"
            className="w-full justify-start h-10 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
          >
            <LogOut className="w-4 h-4 mr-3" />
            <span className="font-medium">Sign Out</span>
          </Button>
        </div>
      </div>
      <Modal
        isOpen={showAddTask}
        onClose={() => setShowAddTask(false)}
        title="Create New Task"
        size="lg"
      >
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Task Title
            </label>
            <Input
              value={newTask.title}
              onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
              placeholder="What needs to be done?"
              className="h-11 text-base"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Description
            </label>
            <textarea
              value={newTask.description}
              onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
              className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
              placeholder="Add more details about this task..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Priority Level
              </label>
              <div className="relative">
                <select
                  value={newTask.priority}
                  onChange={(e) => setNewTask({ ...newTask, priority: e.target.value })}
                  className="w-full p-3 pr-8 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer"
                >
                  <option value="low">🟢 Low Priority</option>
                  <option value="medium">🟡 Medium Priority</option>
                  <option value="high">🔴 High Priority</option>
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Due Date
              </label>
              <Input
                type="date"
                value={newTask.deadline}
                onChange={(e) => setNewTask({ ...newTask, deadline: e.target.value })}
                className="h-11"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-100 dark:border-gray-800">
            <Button
              variant="outline"
              onClick={() => setShowAddTask(false)}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddTask}
              className="px-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Task
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        isOpen={showNewProject}
        onClose={() => setShowNewProject(false)}
        title="Create New Project"
        size="sm"
      >
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Project Name
            </label>
            <Input
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              placeholder="Enter project name"
              className="h-11 text-base"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100 dark:border-gray-800">
            <Button
              variant="outline"
              onClick={() => setShowNewProject(false)}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddProject}
              className="px-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              <FolderOpen className="w-4 h-4 mr-2" />
              Create Project
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
