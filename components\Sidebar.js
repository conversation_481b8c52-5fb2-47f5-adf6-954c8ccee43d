import { useState } from 'react';
import { signOut } from 'firebase/auth';
import { auth } from '../lib/firebase-config';
import { useAuthStore, useTaskStore, useUIStore } from '../lib/store';
import { Button } from './ui/Button';
import { Modal } from './ui/Modal';
import { Input } from './ui/Input';
import { 
  Plus, 
  MessageCircle, 
  LogOut, 
  Moon, 
  Sun, 
  Calendar,
  BarChart3,
  FolderOpen,
  User,
  Settings
} from 'lucide-react';
import toast from 'react-hot-toast';
export default function Sidebar() {
  const { user } = useAuthStore();
  const { getTaskStats, currentProject, projects, setCurrentProject, addProject } = useTaskStore();
  const { theme, toggleTheme, setShowCalendar, setShowAnalytics, setAiChatOpen } = useUIStore();
  const [showAddTask, setShowAddTask] = useState(false);
  const [showNewProject, setShowNewProject] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium',
    deadline: ''
  });
  const stats = getTaskStats();
  const handleLogout = async () => {
    try {
      await signOut(auth);
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };
  const handleAddTask = () => {
    if (!newTask.title.trim()) {
      toast.error('Please enter a task title');
      return;
    }
    useTaskStore.getState().addTask({
      ...newTask,
      status: 'todo',
      project: currentProject
    });
    setNewTask({ title: '', description: '', priority: 'medium', deadline: '' });
    setShowAddTask(false);
    toast.success('Task added successfully');
  };
  const handleAddProject = () => {
    if (!newProjectName.trim()) {
      toast.error('Please enter a project name');
      return;
    }
    if (projects.includes(newProjectName)) {
      toast.error('Project already exists');
      return;
    }
    addProject(newProjectName);
    setCurrentProject(newProjectName);
    setNewProjectName('');
    setShowNewProject(false);
    toast.success('Project created successfully');
  };
  return (
    <>
      <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                {user?.displayName || 'User'}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {user?.email}
              </p>
            </div>
          </div>
        </div>
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900 dark:text-white">Project</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNewProject(true)}
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <select
            value={currentProject}
            onChange={(e) => setCurrentProject(e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            {projects.map(project => (
              <option key={project} value={project}>
                {project === 'default' ? 'Personal' : project}
              </option>
            ))}
          </select>
        </div>
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-white mb-4">Task Overview</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Total</span>
              <span className="font-medium text-gray-900 dark:text-white">{stats.total}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Completed</span>
              <span className="font-medium text-green-600">{stats.completed}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">In Progress</span>
              <span className="font-medium text-yellow-600">{stats.inProgress}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">To Do</span>
              <span className="font-medium text-blue-600">{stats.todo}</span>
            </div>
          </div>
        </div>
        <div className="flex-1 p-6">
          <div className="space-y-2">
            <Button
              onClick={() => setShowAddTask(true)}
              className="w-full justify-start"
              variant="default"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
            <Button
              onClick={() => setAiChatOpen(true)}
              className="w-full justify-start"
              variant="outline"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Talk to Assistant
            </Button>
            <Button
              onClick={() => setShowCalendar(true)}
              className="w-full justify-start"
              variant="ghost"
            >
              <Calendar className="w-4 h-4 mr-2" />
              Calendar View
            </Button>
            <Button
              onClick={() => setShowAnalytics(true)}
              className="w-full justify-start"
              variant="ghost"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Analytics
            </Button>
          </div>
        </div>
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <Button
              onClick={toggleTheme}
              variant="ghost"
              size="sm"
            >
              {theme === 'light' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
            </Button>
            <Button
              onClick={handleLogout}
              variant="ghost"
              size="sm"
              className="text-red-600 hover:text-red-700"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
      <Modal
        isOpen={showAddTask}
        onClose={() => setShowAddTask(false)}
        title="Add New Task"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <Input
              value={newTask.title}
              onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
              placeholder="Enter task title"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={newTask.description}
              onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              rows={3}
              placeholder="Enter task description"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Priority
              </label>
              <select
                value={newTask.priority}
                onChange={(e) => setNewTask({ ...newTask, priority: e.target.value })}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deadline
              </label>
              <Input
                type="date"
                value={newTask.deadline}
                onChange={(e) => setNewTask({ ...newTask, deadline: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setShowAddTask(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTask}>
              Add Task
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        isOpen={showNewProject}
        onClose={() => setShowNewProject(false)}
        title="Create New Project"
        size="sm"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Project Name
            </label>
            <Input
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              placeholder="Enter project name"
            />
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setShowNewProject(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddProject}>
              Create Project
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
