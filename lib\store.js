import { create } from 'zustand';
import { persist } from 'zustand/middleware';
export const useAuthStore = create((set, get) => ({
  user: null,
  loading: true,
  setUser: (user) => set({ user, loading: false }),
  setLoading: (loading) => set({ loading }),
  logout: () => set({ user: null, loading: false })
}));
export const useTaskStore = create(
  persist(
    (set, get) => ({
      tasks: [],
      todoTasks: [],
      currentProject: 'default',
      projects: ['default'],
      addTask: (task) => set((state) => ({
        tasks: [...state.tasks, { ...task, id: Date.now().toString(), createdAt: new Date().toISOString() }]
      })),
      updateTask: (taskId, updates) => set((state) => ({
        tasks: state.tasks.map(task => 
          task.id === taskId ? { ...task, ...updates, updatedAt: new Date().toISOString() } : task
        )
      })),
      deleteTask: (taskId) => set((state) => ({
        tasks: state.tasks.filter(task => task.id !== taskId)
      })),
      moveTask: (taskId, newStatus) => set((state) => ({
        tasks: state.tasks.map(task =>
          task.id === taskId ? { ...task, status: newStatus, updatedAt: new Date().toISOString() } : task
        )
      })),
      addTodoTask: (task) => set((state) => ({
        todoTasks: [...state.todoTasks, { ...task, id: Date.now().toString(), completed: false, createdAt: new Date().toISOString() }]
      })),
      updateTodoTask: (taskId, updates) => set((state) => ({
        todoTasks: state.todoTasks.map(task =>
          task.id === taskId ? { ...task, ...updates, updatedAt: new Date().toISOString() } : task
        )
      })),
      deleteTodoTask: (taskId) => set((state) => ({
        todoTasks: state.todoTasks.filter(task => task.id !== taskId)
      })),
      reorderTodoTasks: (newOrder) => set({ todoTasks: newOrder }),
      setCurrentProject: (project) => set({ currentProject: project }),
      addProject: (project) => set((state) => ({
        projects: [...state.projects, project]
      })),
      getTasksByStatus: (status) => {
        const { tasks, currentProject } = get();
        return tasks.filter(task => task.status === status && task.project === currentProject);
      },
      getTaskStats: () => {
        const { tasks, todoTasks, currentProject } = get();
        const projectTasks = tasks.filter(task => task.project === currentProject);
        const completedTasks = projectTasks.filter(task => task.status === 'done').length;
        const inProgressTasks = projectTasks.filter(task => task.status === 'in-progress').length;
        const todoTasksCount = projectTasks.filter(task => task.status === 'todo').length;
        const completedTodoTasks = todoTasks.filter(task => task.completed).length;
        return {
          total: projectTasks.length + todoTasks.length,
          completed: completedTasks + completedTodoTasks,
          inProgress: inProgressTasks,
          todo: todoTasksCount + todoTasks.filter(task => !task.completed).length,
          completionRate: projectTasks.length > 0 ? Math.round((completedTasks / projectTasks.length) * 100) : 0
        };
      }
    }),
    {
      name: 'focusflow-tasks',
      partialize: (state) => ({
        tasks: state.tasks,
        todoTasks: state.todoTasks,
        currentProject: state.currentProject,
        projects: state.projects
      })
    }
  )
);
export const useUIStore = create(
  persist(
    (set, get) => ({
      theme: 'light',
      sidebarCollapsed: false,
      showCalendar: false,
      showAnalytics: false,
      aiChatOpen: false,
      notifications: true,
      toggleTheme: () => set((state) => ({
        theme: state.theme === 'light' ? 'dark' : 'light'
      })),
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setShowCalendar: (show) => set({ showCalendar: show }),
      setShowAnalytics: (show) => set({ showAnalytics: show }),
      setAiChatOpen: (open) => set({ aiChatOpen: open }),
      setNotifications: (enabled) => set({ notifications: enabled })
    }),
    {
      name: 'focusflow-ui',
      partialize: (state) => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        notifications: state.notifications
      })
    }
  )
);
export const useTimeTrackingStore = create(
  persist(
    (set, get) => ({
      activeTimer: null,
      timeEntries: [],
      startTimer: (taskId, taskTitle) => {
        const { activeTimer } = get();
        if (activeTimer) {
          get().stopTimer();
        }
        set({
          activeTimer: {
            taskId,
            taskTitle,
            startTime: Date.now()
          }
        });
      },
      stopTimer: () => {
        const { activeTimer } = get();
        if (!activeTimer) return;
        const endTime = Date.now();
        const duration = endTime - activeTimer.startTime;
        set((state) => ({
          activeTimer: null,
          timeEntries: [...state.timeEntries, {
            id: Date.now().toString(),
            taskId: activeTimer.taskId,
            taskTitle: activeTimer.taskTitle,
            startTime: activeTimer.startTime,
            endTime,
            duration,
            date: new Date().toISOString().split('T')[0]
          }]
        }));
        return duration;
      },
      getTimeSpentOnTask: (taskId) => {
        const { timeEntries } = get();
        return timeEntries
          .filter(entry => entry.taskId === taskId)
          .reduce((total, entry) => total + entry.duration, 0);
      },
      getTodayTimeEntries: () => {
        const { timeEntries } = get();
        const today = new Date().toISOString().split('T')[0];
        return timeEntries.filter(entry => entry.date === today);
      },
      getWeeklyStats: () => {
        const { timeEntries } = get();
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const weeklyEntries = timeEntries.filter(entry => 
          new Date(entry.date) >= weekAgo
        );
        const totalTime = weeklyEntries.reduce((total, entry) => total + entry.duration, 0);
        const dailyStats = {};
        weeklyEntries.forEach(entry => {
          if (!dailyStats[entry.date]) {
            dailyStats[entry.date] = 0;
          }
          dailyStats[entry.date] += entry.duration;
        });
        return {
          totalTime,
          dailyStats,
          averageDaily: totalTime / 7
        };
      }
    }),
    {
      name: 'focusflow-time-tracking',
      partialize: (state) => ({
        timeEntries: state.timeEntries
      })
    }
  )
);
