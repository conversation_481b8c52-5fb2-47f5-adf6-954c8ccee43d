const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

export const openRouterConfig = {
  apiUrl: OPENROUTER_API_URL,
  model: 'anthropic/claude-sonnet-4'
};

export const getOpenRouterHeaders = (apiKey) => ({
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json',
  'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  'X-Title': 'Task Management App'
});

export const createChatCompletion = async (messages, systemPrompt = '', apiKey) => {
  try {
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: getOpenRouterHeaders(apiKey),
      body: JSON.stringify({
        model: openRouterConfig.model,
        messages: [
          ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
          ...messages
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    throw error;
  }
};

export const parseTaskCommand = (userInput) => {
  const input = userInput.toLowerCase().trim();

  // Enhanced create task patterns
  if (input.includes('create') && (input.includes('task') || input.includes('todo'))) {
    const patterns = [
      /create.*(?:task|todo).*called\s+["']?([^"']+)["']?/i,
      /create.*["']?([^"']+)["']?.*(?:task|todo)/i,
      /add.*(?:task|todo).*["']?([^"']+)["']?/i,
      /new.*(?:task|todo).*["']?([^"']+)["']?/i
    ];

    for (const pattern of patterns) {
      const match = input.match(pattern);
      if (match) {
        return {
          action: 'create',
          taskName: match[1].trim(),
          column: 'todo'
        };
      }
    }
  }

  // Enhanced move task patterns
  if (input.includes('move') || input.includes('change') || input.includes('set')) {
    const taskPatterns = [
      /(?:move|change|set).*task\s+["']?([^"']+)["']?.*(?:to|into)\s+(to do|todo|in progress|progress|done|completed)/i,
      /(?:move|change|set).*["']?([^"']+)["']?.*(?:to|into)\s+(to do|todo|in progress|progress|done|completed)/i
    ];

    for (const pattern of taskPatterns) {
      const match = input.match(pattern);
      if (match) {
        const columnMap = {
          'to do': 'todo',
          'todo': 'todo',
          'in progress': 'inprogress',
          'progress': 'inprogress',
          'done': 'done',
          'completed': 'done'
        };
        return {
          action: 'move',
          taskName: match[1].trim(),
          column: columnMap[match[2].toLowerCase()]
        };
      }
    }
  }

  // Enhanced delete task patterns
  if (input.includes('delete') || input.includes('remove')) {
    const patterns = [
      /(?:delete|remove).*task.*["']?([^"']+)["']?/i,
      /(?:delete|remove).*["']?([^"']+)["']?.*task/i
    ];

    for (const pattern of patterns) {
      const match = input.match(pattern);
      if (match) {
        return {
          action: 'delete',
          taskName: match[1].trim()
        };
      }
    }
  }

  return {
    action: 'chat',
    message: userInput
  };
};
