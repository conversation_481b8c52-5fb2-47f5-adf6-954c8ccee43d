const OPENROUTER_API_KEY = process.env.NEXT_PUBLIC_OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const AI_MODELS = {
  CLAUDE_4: 'anthropic/claude-sonnet-4',
  DEEPSEEK: 'deepseek/deepseek-r1-0528',
  GEMINI_PRO: 'google/gemini-pro-1.5',
  O3: 'openai/o3-mini',
  PROSEARCH: 'perplexity/sonar-deep-research'
};
const MODEL_CAPABILITIES = {
  [AI_MODELS.CLAUDE_4]: {
    name: 'Claude 4',
    strengths: ['creative tasks', 'writing', 'analysis', 'planning'],
    use_cases: ['task descriptions', 'project planning', 'creative suggestions']
  },
  [AI_MODELS.DEEPSEEK]: {
    name: 'DeepSeek',
    strengths: ['analytical tasks', 'data processing', 'logical reasoning'],
    use_cases: ['priority analysis', 'time estimation', 'productivity insights']
  },
  [AI_MODELS.GEMINI_PRO]: {
    name: 'Gemini Pro 2.5',
    strengths: ['multimodal tasks', 'general assistance', 'context understanding'],
    use_cases: ['general queries', 'task management', 'scheduling']
  },
  [AI_MODELS.O3]: {
    name: 'O3 Mini',
    strengths: ['quick responses', 'simple tasks', 'efficiency'],
    use_cases: ['quick edits', 'simple commands', 'status updates']
  },
  [AI_MODELS.PROSEARCH]: {
    name: 'ProSearch',
    strengths: ['research', 'information gathering', 'web search'],
    use_cases: ['research tasks', 'information lookup', 'context gathering']
  }
};
export const selectModelForTask = (taskType) => {
  const taskTypeMap = {
    'create': AI_MODELS.CLAUDE_4,
    'edit': AI_MODELS.O3,
    'analyze': AI_MODELS.DEEPSEEK,
    'research': AI_MODELS.PROSEARCH,
    'schedule': AI_MODELS.GEMINI_PRO,
    'priority': AI_MODELS.DEEPSEEK,
    'general': AI_MODELS.GEMINI_PRO
  };
  return taskTypeMap[taskType] || AI_MODELS.GEMINI_PRO;
};
export const makeOpenRouterRequest = async (model, messages, maxTokens = 1000) => {
  try {
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'FocusFlow AI'
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7
      })
    });
    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }
    const data = await response.json();
    return data.choices[0]?.message?.content || 'No response received';
  } catch (error) {
    console.error('OpenRouter API Error:', error);
    throw error;
  }
};
export { AI_MODELS, MODEL_CAPABILITIES, OPENROUTER_API_KEY };