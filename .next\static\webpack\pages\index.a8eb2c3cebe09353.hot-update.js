"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanBoard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @dnd-kit/utilities */ \"./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle2,Circle,Clock,Edit,Pause,Play,PlayCircle,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,CheckCircle2,Circle,Clock,Edit,Pause,Play,PlayCircle,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _utils_exportData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../utils/exportData */ \"./utils/exportData.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TaskCard(param) {\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const { activeTimer, startTimer, stopTimer } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore)();\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTask, setEditTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(task);\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__.CSS.Transform.toString(transform),\n        transition\n    };\n    const handleEdit = ()=>{\n        updateTask(task.id, editTask);\n        setShowEdit(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task updated successfully\");\n    };\n    const handleDelete = ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            deleteTask(task.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task deleted successfully\");\n        }\n    };\n    const handleTimer = ()=>{\n        if ((activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id) {\n            stopTimer();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer stopped\");\n        } else {\n            startTimer(task.id, task.title);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer started\");\n        }\n    };\n    const isTimerActive = (activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"mb-3 \".concat(isDragging ? \"opacity-50\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"cursor-move hover:shadow-md transition-shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"pb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: task.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleTimer();\n                                                },\n                                                children: isTimerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Pause, {\n                                                    className: \"h-3 w-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Play, {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Edit, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Trash2, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"pt-0\",\n                            children: [\n                                task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400 mb-2\",\n                                    children: task.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: task.priority,\n                                                    className: \"text-xs\",\n                                                    children: task.priority\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this),\n                                                task.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Calendar, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(task.deadline)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTimerActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Clock, {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false),\n                title: \"Edit Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                    value: editTask.title,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            title: e.target.value\n                                        })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: editTask.description,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: editTask.priority,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                            type: \"date\",\n                                            value: editTask.deadline,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowEdit(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    onClick: handleEdit,\n                                    children: \"Save Changes\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TaskCard, \"phrOKTed3xFlYlOxG+d3h1gAb1k=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore,\n        _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable\n    ];\n});\n_c = TaskCard;\nfunction KanbanColumn(param) {\n    let { title, status, tasks, icon: Icon, color } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable)({\n        id: status\n    });\n    const colorClasses = {\n        blue: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n        yellow: \"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30\",\n        green: \"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-80\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(colorClasses[color], \" border rounded-xl p-5 transition-all duration-200 \").concat(isOver ? \"ring-2 ring-blue-400 ring-opacity-50\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center \".concat(color === \"blue\" ? \"bg-blue-500\" : color === \"yellow\" ? \"bg-yellow-500\" : \"bg-green-500\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white text-lg\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-sm font-medium \".concat(color === \"blue\" ? \"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\" : color === \"yellow\" ? \"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300\" : \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\"),\n                            children: tasks.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: setNodeRef,\n                    className: \"space-y-3 min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                        children: [\n                            tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                                    task: task\n                                }, task.id, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)),\n                            tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                    children: 'Drop tasks here or click \"Add Task\" to get started'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n_s1(KanbanColumn, \"B0t40knSjPgPvDEIZEaFzamr7vc=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable\n    ];\n});\n_c1 = KanbanColumn;\nfunction KanbanBoard() {\n    _s2();\n    const { tasks, moveTask, currentProject, getTaskStats } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const projectTasks = tasks.filter((task)=>task.project === currentProject);\n    const todoTasks = projectTasks.filter((task)=>task.status === \"todo\");\n    const inProgressTasks = projectTasks.filter((task)=>task.status === \"in-progress\");\n    const doneTasks = projectTasks.filter((task)=>task.status === \"done\");\n    const handleDragStart = (event)=>{\n        setActiveId(event.active.id);\n    };\n    const handleDragEnd = (event)=>{\n        var _over_data_current_sortable, _over_data_current, _over_data;\n        const { active, over } = event;\n        setActiveId(null);\n        if (!over) return;\n        const activeTask = projectTasks.find((task)=>task.id === active.id);\n        if (!activeTask) return;\n        const overContainer = ((_over_data = over.data) === null || _over_data === void 0 ? void 0 : (_over_data_current = _over_data.current) === null || _over_data_current === void 0 ? void 0 : (_over_data_current_sortable = _over_data_current.sortable) === null || _over_data_current_sortable === void 0 ? void 0 : _over_data_current_sortable.containerId) || over.id;\n        let newStatus = activeTask.status;\n        if (overContainer.includes(\"todo\")) newStatus = \"todo\";\n        else if (overContainer.includes(\"in-progress\")) newStatus = \"in-progress\";\n        else if (overContainer.includes(\"done\")) newStatus = \"done\";\n        if (newStatus !== activeTask.status) {\n            moveTask(active.id, newStatus);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task moved to \".concat(newStatus.replace(\"-\", \" \")));\n        }\n    };\n    const activeTask = activeId ? projectTasks.find((task)=>task.id === activeId) : null;\n    const handleExportCSV = ()=>{\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToCSV)(projectTasks, \"\".concat(currentProject, \"-kanban-tasks\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to CSV successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    const handleExportPDF = ()=>{\n        const stats = getTaskStats();\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToPDF)(projectTasks, stats, \"\".concat(currentProject, \"-kanban-report\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to PDF successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Kanban Board\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Drag and drop tasks between columns to update their status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportCSV,\n                                    children: \"Export CSV\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportPDF,\n                                    children: \"Export PDF\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 overflow-x-auto pb-4 kanban-board\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"To Do\",\n                                status: \"todo\",\n                                tasks: todoTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Circle,\n                                color: \"blue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"In Progress\",\n                                status: \"in-progress\",\n                                tasks: inProgressTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.PlayCircle,\n                                color: \"yellow\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"Done\",\n                                status: \"done\",\n                                tasks: doneTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.CheckCircle2,\n                                color: \"green\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                            task: activeTask\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 393,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n_s2(KanbanBoard, \"dKIcBJsvhRbKqNVhbc0ByDX8JMo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors\n    ];\n});\n_c2 = KanbanBoard;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TaskCard\");\n$RefreshReg$(_c1, \"KanbanColumn\");\n$RefreshReg$(_c2, \"KanbanBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n"));

/***/ })

});