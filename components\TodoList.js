import { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTaskStore } from '../lib/store';
import { Card, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { 
  Plus, 
  Check, 
  X, 
  GripVertical, 
  Edit, 
  Trash2 
} from 'lucide-react';
import toast from 'react-hot-toast';
function TodoItem({ task, onToggle, onEdit, onDelete }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(task.title);
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const handleEdit = () => {
    if (editText.trim()) {
      onEdit(task.id, { title: editText.trim() });
      setIsEditing(false);
    }
  };
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleEdit();
    } else if (e.key === 'Escape') {
      setEditText(task.title);
      setIsEditing(false);
    }
  };
  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`mb-2 ${isDragging ? 'opacity-50' : ''}`}
    >
      <Card className="hover:shadow-sm transition-shadow">
        <CardContent className="p-3">
          <div className="flex items-center space-x-3">
            <div
              {...attributes}
              {...listeners}
              className="cursor-grab hover:text-gray-600 dark:hover:text-gray-400"
            >
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
            <button
              onClick={() => onToggle(task.id)}
              className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                task.completed
                  ? 'bg-green-500 border-green-500 text-white'
                  : 'border-gray-300 hover:border-green-500'
              }`}
            >
              {task.completed && <Check className="h-3 w-3" />}
            </button>
            <div className="flex-1">
              {isEditing ? (
                <Input
                  value={editText}
                  onChange={(e) => setEditText(e.target.value)}
                  onKeyDown={handleKeyPress}
                  onBlur={handleEdit}
                  className="text-sm"
                  autoFocus
                />
              ) : (
                <span
                  className={`text-sm ${
                    task.completed
                      ? 'line-through text-gray-500 dark:text-gray-400'
                      : 'text-gray-900 dark:text-white'
                  }`}
                >
                  {task.title}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-red-500 hover:text-red-700"
                onClick={() => onDelete(task.id)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
export default function TodoList() {
  const { 
    todoTasks, 
    addTodoTask, 
    updateTodoTask, 
    deleteTodoTask, 
    reorderTodoTasks 
  } = useTaskStore();
  const [newTodo, setNewTodo] = useState('');
  const [showCompleted, setShowCompleted] = useState(true);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  const handleAddTodo = () => {
    if (!newTodo.trim()) {
      toast.error('Please enter a todo item');
      return;
    }
    addTodoTask({
      title: newTodo.trim()
    });
    setNewTodo('');
    toast.success('Todo added successfully');
  };
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddTodo();
    }
  };
  const handleToggleTodo = (taskId) => {
    const task = todoTasks.find(t => t.id === taskId);
    if (task) {
      updateTodoTask(taskId, { completed: !task.completed });
      toast.success(task.completed ? 'Todo marked as incomplete' : 'Todo completed!');
    }
  };
  const handleEditTodo = (taskId, updates) => {
    updateTodoTask(taskId, updates);
    toast.success('Todo updated successfully');
  };
  const handleDeleteTodo = (taskId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      deleteTodoTask(taskId);
      toast.success('Todo deleted successfully');
    }
  };
  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = todoTasks.findIndex(task => task.id === active.id);
      const newIndex = todoTasks.findIndex(task => task.id === over.id);
      reorderTodoTasks(arrayMove(todoTasks, oldIndex, newIndex));
    }
  };
  const filteredTodos = showCompleted 
    ? todoTasks 
    : todoTasks.filter(task => !task.completed);
  const completedCount = todoTasks.filter(task => task.completed).length;
  const totalCount = todoTasks.length;
  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Quick Todos
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {completedCount} of {totalCount} completed
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCompleted(!showCompleted)}
          >
            {showCompleted ? 'Hide Completed' : 'Show All'}
          </Button>
        </div>
        <div className="flex space-x-2">
          <Input
            value={newTodo}
            onChange={(e) => setNewTodo(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Add a new todo..."
            className="flex-1"
          />
          <Button onClick={handleAddTodo}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>
      </div>
      <div className="max-w-2xl">
        {filteredTodos.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                {todoTasks.length === 0 
                  ? 'No todos yet. Add one above to get started!'
                  : 'All todos completed! 🎉'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext 
              items={filteredTodos.map(task => task.id)} 
              strategy={verticalListSortingStrategy}
            >
              {filteredTodos.map((task) => (
                <TodoItem
                  key={task.id}
                  task={task}
                  onToggle={handleToggleTodo}
                  onEdit={handleEditTodo}
                  onDelete={handleDeleteTodo}
                />
              ))}
            </SortableContext>
          </DndContext>
        )}
      </div>
    </div>
  );
}
