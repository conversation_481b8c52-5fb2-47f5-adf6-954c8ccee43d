"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanBoard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @dnd-kit/utilities */ \"./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle2,Circle,Clock,Edit,Pause,Play,PlayCircle,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,CheckCircle2,Circle,Clock,Edit,Pause,Play,PlayCircle,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _utils_exportData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../utils/exportData */ \"./utils/exportData.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TaskCard(param) {\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const { activeTimer, startTimer, stopTimer } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore)();\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTask, setEditTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(task);\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__.CSS.Transform.toString(transform),\n        transition\n    };\n    const handleEdit = ()=>{\n        updateTask(task.id, editTask);\n        setShowEdit(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task updated successfully\");\n    };\n    const handleDelete = ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            deleteTask(task.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task deleted successfully\");\n        }\n    };\n    const handleTimer = ()=>{\n        if ((activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id) {\n            stopTimer();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer stopped\");\n        } else {\n            startTimer(task.id, task.title);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer started\");\n        }\n    };\n    const isTimerActive = (activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"mb-3 \".concat(isDragging ? \"opacity-50 rotate-2 scale-105\" : \"\", \" transition-all duration-200\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"cursor-move hover:shadow-lg hover:-translate-y-1 transition-all duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 group\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 dark:text-white text-sm leading-tight pr-2\",\n                                        children: task.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-7 w-7 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleTimer();\n                                                },\n                                                children: isTimerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Pause, {\n                                                    className: \"h-3 w-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Play, {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-7 w-7 hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Edit, {\n                                                    className: \"h-3 w-3 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-7 w-7 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Trash2, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                children: task.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                variant: task.priority,\n                                                className: \"text-xs font-medium \".concat(task.priority === \"high\" ? \"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300\" : task.priority === \"medium\" ? \"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300\" : \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\"),\n                                                children: [\n                                                    task.priority === \"high\" ? \"\\uD83D\\uDD34\" : task.priority === \"medium\" ? \"\\uD83D\\uDFE1\" : \"\\uD83D\\uDFE2\",\n                                                    \" \",\n                                                    task.priority\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            task.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-xs text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Calendar, {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(task.deadline)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    isTimerActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20 px-2 py-1 rounded-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Clock, {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Active\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false),\n                title: \"Edit Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                    value: editTask.title,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            title: e.target.value\n                                        })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: editTask.description,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: editTask.priority,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                            type: \"date\",\n                                            value: editTask.deadline,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowEdit(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    onClick: handleEdit,\n                                    children: \"Save Changes\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TaskCard, \"phrOKTed3xFlYlOxG+d3h1gAb1k=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore,\n        _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable\n    ];\n});\n_c = TaskCard;\nfunction KanbanColumn(param) {\n    let { title, status, tasks, icon: Icon, color } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable)({\n        id: status\n    });\n    const colorClasses = {\n        blue: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n        yellow: \"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30\",\n        green: \"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-80\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(colorClasses[color], \" border rounded-xl p-5 transition-all duration-200 \").concat(isOver ? \"ring-2 ring-blue-400 ring-opacity-50\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center \".concat(color === \"blue\" ? \"bg-blue-500\" : color === \"yellow\" ? \"bg-yellow-500\" : \"bg-green-500\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white text-lg\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-sm font-medium \".concat(color === \"blue\" ? \"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\" : color === \"yellow\" ? \"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300\" : \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\"),\n                            children: tasks.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: setNodeRef,\n                    className: \"space-y-3 min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                        children: [\n                            tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                                    task: task\n                                }, task.id, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)),\n                            tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                    children: 'Drop tasks here or click \"Add Task\" to get started'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 286,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s1(KanbanColumn, \"B0t40knSjPgPvDEIZEaFzamr7vc=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useDroppable\n    ];\n});\n_c1 = KanbanColumn;\nfunction KanbanBoard() {\n    _s2();\n    const { tasks, moveTask, currentProject, getTaskStats } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const projectTasks = tasks.filter((task)=>task.project === currentProject);\n    const todoTasks = projectTasks.filter((task)=>task.status === \"todo\");\n    const inProgressTasks = projectTasks.filter((task)=>task.status === \"in-progress\");\n    const doneTasks = projectTasks.filter((task)=>task.status === \"done\");\n    const handleDragStart = (event)=>{\n        setActiveId(event.active.id);\n    };\n    const handleDragEnd = (event)=>{\n        const { active, over } = event;\n        setActiveId(null);\n        if (!over) return;\n        const activeTask = projectTasks.find((task)=>task.id === active.id);\n        if (!activeTask) return;\n        // Get the container ID from the droppable area\n        const overId = over.id;\n        let newStatus = activeTask.status;\n        // Determine new status based on drop target\n        if (overId === \"todo\") newStatus = \"todo\";\n        else if (overId === \"in-progress\") newStatus = \"in-progress\";\n        else if (overId === \"done\") newStatus = \"done\";\n        // Only update if status actually changed\n        if (newStatus !== activeTask.status) {\n            moveTask(active.id, newStatus);\n            const statusNames = {\n                \"todo\": \"To Do\",\n                \"in-progress\": \"In Progress\",\n                \"done\": \"Done\"\n            };\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task moved to \".concat(statusNames[newStatus]));\n        }\n    };\n    const activeTask = activeId ? projectTasks.find((task)=>task.id === activeId) : null;\n    const handleExportCSV = ()=>{\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToCSV)(projectTasks, \"\".concat(currentProject, \"-kanban-tasks\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to CSV successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    const handleExportPDF = ()=>{\n        const stats = getTaskStats();\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToPDF)(projectTasks, stats, \"\".concat(currentProject, \"-kanban-report\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to PDF successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Kanban Board\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Drag and drop tasks between columns to update their status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportCSV,\n                                    children: \"Export CSV\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportPDF,\n                                    children: \"Export PDF\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 overflow-x-auto pb-4 kanban-board\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"To Do\",\n                                status: \"todo\",\n                                tasks: todoTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Circle,\n                                color: \"blue\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"In Progress\",\n                                status: \"in-progress\",\n                                tasks: inProgressTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.PlayCircle,\n                                color: \"yellow\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"Done\",\n                                status: \"done\",\n                                tasks: doneTasks,\n                                icon: _barrel_optimize_names_Calendar_CheckCircle2_Circle_Clock_Edit_Pause_Play_PlayCircle_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.CheckCircle2,\n                                color: \"green\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                            task: activeTask\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 415,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 364,\n        columnNumber: 5\n    }, this);\n}\n_s2(KanbanBoard, \"dKIcBJsvhRbKqNVhbc0ByDX8JMo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors\n    ];\n});\n_c2 = KanbanBoard;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TaskCard\");\n$RefreshReg$(_c1, \"KanbanColumn\");\n$RefreshReg$(_c2, \"KanbanBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n"));

/***/ })

});