import { useState, useEffect, useRef } from 'react';
import { useTaskStore } from '../lib/store';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { 
  Mi<PERSON>, 
  MicOff, 
  Volume2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import toast from 'react-hot-toast';
export default function VoiceCommands() {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const recognitionRef = useRef(null);
  const { addTask, addTodoTask, currentProject } = useTaskStore();
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognition) {
        setIsSupported(true);
        recognitionRef.current = new SpeechRecognition();
        const recognition = recognitionRef.current;
        recognition.continuous = false;
        recognition.interimResults = true;
        recognition.lang = 'en-US';
        recognition.onstart = () => {
          setIsListening(true);
          setTranscript('');
        };
        recognition.onresult = (event) => {
          let finalTranscript = '';
          let interimTranscript = '';
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            const confidence = event.results[i][0].confidence;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
              setConfidence(confidence);
            } else {
              interimTranscript += transcript;
            }
          }
          setTranscript(finalTranscript || interimTranscript);
          if (finalTranscript) {
            processVoiceCommand(finalTranscript, confidence);
          }
        };
        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
          let errorMessage = 'Voice recognition error occurred';
          switch (event.error) {
            case 'no-speech':
              errorMessage = 'No speech detected. Please try again.';
              break;
            case 'audio-capture':
              errorMessage = 'Microphone not accessible. Please check permissions.';
              break;
            case 'not-allowed':
              errorMessage = 'Microphone permission denied. Please enable microphone access.';
              break;
            case 'network':
              errorMessage = 'Network error occurred. Please check your connection.';
              break;
          }
          toast.error(errorMessage);
        };
        recognition.onend = () => {
          setIsListening(false);
        };
      }
    }
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, []);
  const processVoiceCommand = (command, confidence) => {
    const lowerCommand = command.toLowerCase().trim();
    if (confidence < 0.7) {
      toast.error('Low confidence in voice recognition. Please try again.');
      return;
    }
    if (lowerCommand.includes('create task') || lowerCommand.includes('add task') || lowerCommand.includes('new task')) {
      const taskTitle = extractTaskTitle(lowerCommand);
      if (taskTitle) {
        const priority = extractPriority(lowerCommand);
        const deadline = extractDeadline(lowerCommand);
        addTask({
          title: taskTitle,
          description: `Created via voice command: "${command}"`,
          priority: priority || 'medium',
          deadline: deadline || '',
          status: 'todo',
          project: currentProject
        });
        toast.success(`Task "${taskTitle}" created successfully!`);
        speak(`Task ${taskTitle} has been created`);
      } else {
        toast.error('Could not extract task title from voice command');
        speak('Sorry, I could not understand the task title');
      }
    } else if (lowerCommand.includes('add todo') || lowerCommand.includes('quick todo')) {
      const todoTitle = extractTodoTitle(lowerCommand);
      if (todoTitle) {
        addTodoTask({ title: todoTitle });
        toast.success(`Todo "${todoTitle}" added successfully!`);
        speak(`Todo ${todoTitle} has been added`);
      } else {
        toast.error('Could not extract todo title from voice command');
        speak('Sorry, I could not understand the todo title');
      }
    } else {
      toast.error('Voice command not recognized. Try saying "Create task [task name]" or "Add todo [todo item]"');
      speak('Sorry, I did not understand that command');
    }
  };
  const extractTaskTitle = (command) => {
    const patterns = [
      /create task (?:called |named |titled )?(.+?)(?:\s+with|\s+priority|\s+due|$)/i,
      /add task (?:called |named |titled )?(.+?)(?:\s+with|\s+priority|\s+due|$)/i,
      /new task (?:called |named |titled )?(.+?)(?:\s+with|\s+priority|\s+due|$)/i
    ];
    for (const pattern of patterns) {
      const match = command.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    return null;
  };
  const extractTodoTitle = (command) => {
    const patterns = [
      /add todo (.+?)$/i,
      /quick todo (.+?)$/i
    ];
    for (const pattern of patterns) {
      const match = command.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }
    return null;
  };
  const extractPriority = (command) => {
    if (command.includes('high priority') || command.includes('urgent')) return 'high';
    if (command.includes('low priority')) return 'low';
    if (command.includes('medium priority') || command.includes('normal priority')) return 'medium';
    return null;
  };
  const extractDeadline = (command) => {
    if (command.includes('due today') || command.includes('today')) {
      return new Date().toISOString().split('T')[0];
    }
    if (command.includes('due tomorrow') || command.includes('tomorrow')) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString().split('T')[0];
    }
    return null;
  };
  const speak = (text) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      speechSynthesis.speak(utterance);
    }
  };
  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        toast.success('Voice recognition started. Speak now...');
      } catch (error) {
        console.error('Error starting recognition:', error);
        toast.error('Failed to start voice recognition');
      }
    }
  };
  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      toast.info('Voice recognition stopped');
    }
  };
  if (!isSupported) {
    return (
      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-yellow-600" />
          <div>
            <p className="font-medium text-yellow-800 dark:text-yellow-200">
              Voice Commands Not Supported
            </p>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Your browser doesn't support the Web Speech API. Try using Chrome or Edge.
            </p>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="p-4 bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950/20 dark:to-indigo-950/20 border border-purple-100 dark:border-purple-900/30 rounded-xl">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
            <Volume2 className="w-3 h-3 text-white" />
          </div>
          <div>
            <p className="text-sm font-semibold text-purple-800 dark:text-purple-200">
              Voice Commands
            </p>
            <p className="text-xs text-purple-600 dark:text-purple-300">
              Hands-free task creation
            </p>
          </div>
        </div>
        <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
          Beta
        </Badge>
      </div>
      <div className="space-y-3">
        <Button
          onClick={isListening ? stopListening : startListening}
          variant={isListening ? "destructive" : "default"}
          size="sm"
          className={`w-full h-9 ${isListening
            ? 'bg-red-500 hover:bg-red-600 text-white'
            : 'bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white'
          }`}
        >
          {isListening ? (
            <>
              <MicOff className="w-4 h-4 mr-2" />
              <span className="font-medium">Stop Listening</span>
            </>
          ) : (
            <>
              <Mic className="w-4 h-4 mr-2" />
              <span className="font-medium">Start Voice Command</span>
            </>
          )}
        </Button>

        {isListening && (
          <div className="flex items-center justify-center space-x-2 p-2 bg-red-50 dark:bg-red-950/20 rounded-lg">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-red-700 dark:text-red-300">
              Listening for commands...
            </span>
          </div>
        )}
      </div>
      {transcript && (
        <div className="mt-4 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded">
          <div className="flex items-start space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Recognized:
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                "{transcript}"
              </p>
              {confidence > 0 && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Confidence: {Math.round(confidence * 100)}%
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="mt-4 text-xs text-blue-600 dark:text-blue-400">
        <p className="font-medium mb-1">Supported commands:</p>
        <ul className="space-y-1">
          <li>• "Create task [task name]"</li>
          <li>• "Add task [task name] with high priority"</li>
          <li>• "New task [task name] due tomorrow"</li>
          <li>• "Add todo [todo item]"</li>
          <li>• "Quick todo [todo item]"</li>
        </ul>
      </div>
    </div>
  );
}
