import { makeOpenRouterRequest, selectModelForTask } from './openrouter-config';
import { useTaskStore } from './store';
export class AIAssistant {
  constructor() {
    this.conversationHistory = [];
  }
  async processCommand(userInput) {
    try {
      const commandType = this.detectCommandType(userInput);
      const model = selectModelForTask(commandType);
      const systemPrompt = this.getSystemPrompt();
      const messages = [
        { role: 'system', content: systemPrompt },
        ...this.conversationHistory,
        { role: 'user', content: userInput }
      ];
      const response = await makeOpenRouterRequest(model, messages);
      this.conversationHistory.push(
        { role: 'user', content: userInput },
        { role: 'assistant', content: response }
      );
      if (this.conversationHistory.length > 20) {
        this.conversationHistory = this.conversationHistory.slice(-20);
      }
      const actionResult = await this.executeAction(userInput, response);
      return {
        response,
        action: actionResult,
        model: model
      };
    } catch (error) {
      console.error('AI Assistant Error:', error);
      return {
        response: 'I apologize, but I encountered an error processing your request. Please try again.',
        action: null,
        error: error.message
      };
    }
  }
  detectCommandType(input) {
    const lowerInput = input.toLowerCase();
    if (lowerInput.includes('create') || lowerInput.includes('add') || lowerInput.includes('new task')) {
      return 'create';
    }
    if (lowerInput.includes('edit') || lowerInput.includes('update') || lowerInput.includes('change')) {
      return 'edit';
    }
    if (lowerInput.includes('analyze') || lowerInput.includes('priority') || lowerInput.includes('reorder')) {
      return 'analyze';
    }
    if (lowerInput.includes('research') || lowerInput.includes('find') || lowerInput.includes('search')) {
      return 'research';
    }
    if (lowerInput.includes('schedule') || lowerInput.includes('calendar') || lowerInput.includes('deadline')) {
      return 'schedule';
    }
    return 'general';
  }
  getSystemPrompt() {
    const { tasks, todoTasks, currentProject } = useTaskStore.getState();
    const projectTasks = tasks.filter(task => task.project === currentProject);
    return `You are FocusFlow AI, an intelligent productivity assistant. You help users manage their tasks, projects, and productivity.

Current Context:
- Current Project: ${currentProject}
- Kanban Tasks: ${projectTasks.length} (${projectTasks.filter(t => t.status === 'todo').length} todo, ${projectTasks.filter(t => t.status === 'in-progress').length} in progress, ${projectTasks.filter(t => t.status === 'done').length} done)
- Quick Todos: ${todoTasks.length} (${todoTasks.filter(t => !t.completed).length} pending, ${todoTasks.filter(t => t.completed).length} completed)

You can help with:
1. Creating new tasks with titles, descriptions, priorities, and deadlines
2. Editing existing tasks
3. Moving tasks between columns (todo, in-progress, done)
4. Analyzing task priorities and suggesting improvements
5. Providing productivity insights and recommendations
6. Scheduling and deadline management
7. General task management advice

When users ask you to create or modify tasks, provide a clear response and indicate what action was taken. Be concise but helpful, and always confirm actions taken.

For task creation commands, extract:
- Title (required)
- Description (optional)
- Priority (low/medium/high, default: medium)
- Deadline (optional, format: YYYY-MM-DD)
- Type (kanban task or quick todo)

Respond in a friendly, professional manner and focus on productivity and task management.`;
  }
  async executeAction(userInput, aiResponse) {
    const lowerInput = userInput.toLowerCase();
    const { addTask, addTodoTask, updateTask, moveTask, tasks, todoTasks, currentProject } = useTaskStore.getState();
    if (lowerInput.includes('create') || lowerInput.includes('add') || lowerInput.includes('new task')) {
      const taskInfo = this.extractTaskInfo(userInput, aiResponse);
      if (taskInfo.title) {
        if (lowerInput.includes('todo') || lowerInput.includes('quick')) {
          addTodoTask({ title: taskInfo.title });
          return { type: 'todo_created', data: taskInfo };
        } else {
          addTask({
            ...taskInfo,
            status: 'todo',
            project: currentProject
          });
          return { type: 'task_created', data: taskInfo };
        }
      }
    }
    if (lowerInput.includes('move') || lowerInput.includes('change status')) {
      const moveInfo = this.extractMoveInfo(userInput);
      if (moveInfo.taskId && moveInfo.newStatus) {
        moveTask(moveInfo.taskId, moveInfo.newStatus);
        return { type: 'task_moved', data: moveInfo };
      }
    }
    if (lowerInput.includes('priority') && lowerInput.includes('reorder')) {
      const projectTasks = tasks.filter(task => task.project === currentProject);
      const sortedTasks = [...projectTasks].sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
      return { type: 'tasks_reordered', data: sortedTasks };
    }
    return null;
  }
  extractTaskInfo(userInput, aiResponse) {
    const titleMatch = userInput.match(/(?:create|add|new task).*?(?:called|named|titled)\s+"([^"]+)"/i) ||
                      userInput.match(/(?:create|add|new task)\s+(.+?)(?:\s+with|\s+due|\s+priority|$)/i);
    const priorityMatch = userInput.match(/priority\s+(high|medium|low)/i);
    const deadlineMatch = userInput.match(/(?:due|deadline|by)\s+(\d{4}-\d{2}-\d{2}|\w+)/i);
    const descriptionMatch = userInput.match(/(?:description|about|details)\s+"([^"]+)"/i);
    return {
      title: titleMatch ? titleMatch[1].trim() : '',
      description: descriptionMatch ? descriptionMatch[1] : '',
      priority: priorityMatch ? priorityMatch[1].toLowerCase() : 'medium',
      deadline: deadlineMatch ? this.parseDeadline(deadlineMatch[1]) : ''
    };
  }
  extractMoveInfo(userInput) {
    const taskMatch = userInput.match(/(?:move|change)\s+(?:task\s+)?(.+?)\s+to\s+(todo|in-progress|done)/i);
    if (taskMatch) {
      const taskTitle = taskMatch[1].trim();
      const newStatus = taskMatch[2].toLowerCase().replace(' ', '-');
      const { tasks, currentProject } = useTaskStore.getState();
      const task = tasks.find(t => 
        t.project === currentProject && 
        t.title.toLowerCase().includes(taskTitle.toLowerCase())
      );
      return {
        taskId: task?.id,
        newStatus,
        taskTitle
      };
    }
    return {};
  }
  parseDeadline(dateString) {
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateString;
    }
    const today = new Date();
    if (dateString.toLowerCase() === 'today') {
      return today.toISOString().split('T')[0];
    }
    if (dateString.toLowerCase() === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow.toISOString().split('T')[0];
    }
    return '';
  }
  getSuggestions(currentTasks) {
    const suggestions = [];
    const overdueTasks = currentTasks.filter(task => {
      if (!task.deadline) return false;
      return new Date(task.deadline) < new Date() && task.status !== 'done';
    });
    if (overdueTasks.length > 0) {
      suggestions.push(`You have ${overdueTasks.length} overdue task(s). Consider prioritizing them.`);
    }
    const highPriorityTasks = currentTasks.filter(task => 
      task.priority === 'high' && task.status !== 'done'
    );
    if (highPriorityTasks.length > 0) {
      suggestions.push(`Focus on your ${highPriorityTasks.length} high-priority task(s) first.`);
    }
    const inProgressTasks = currentTasks.filter(task => task.status === 'in-progress');
    if (inProgressTasks.length > 3) {
      suggestions.push('You have many tasks in progress. Consider completing some before starting new ones.');
    }
    return suggestions;
  }
  clearHistory() {
    this.conversationHistory = [];
  }
}
export const aiAssistant = new AIAssistant();
