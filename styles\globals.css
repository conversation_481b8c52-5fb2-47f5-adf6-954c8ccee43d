@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-200;
  }
}

@layer components {
  .sidebar-mobile {
    @apply fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0;
  }

  .sidebar-mobile.closed {
    @apply -translate-x-full;
  }

  .main-content {
    @apply flex-1 overflow-auto lg:ml-0;
  }

  .mobile-overlay {
    @apply fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 lg:hidden;
  }
}

.rbc-calendar {
  @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100;
}

.rbc-header {
  @apply bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100;
}

.rbc-today {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.rbc-off-range-bg {
  @apply bg-gray-100 dark:bg-gray-800;
}

.rbc-event {
  @apply text-white;
}

.rbc-button-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300;
}

.rbc-toolbar {
  @apply text-gray-900 dark:text-gray-100;
}

.rbc-toolbar button {
  @apply text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700;
}

.rbc-toolbar button.rbc-active {
  @apply bg-blue-600 text-white border-blue-600;
}

@media (max-width: 768px) {
  .kanban-board {
    @apply flex-col space-y-4;
  }

  .kanban-column {
    @apply min-w-full;
  }

  .sidebar-mobile {
    @apply w-full;
  }

  .mobile-nav {
    @apply flex lg:hidden items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
  }
}

@media (max-width: 640px) {
  .analytics-grid {
    @apply grid-cols-1;
  }

  .calendar-grid {
    @apply grid-cols-1;
  }

  .task-card {
    @apply text-sm;
  }

  .ai-chat {
    @apply w-full h-full fixed inset-0 z-50;
  }
}

.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

.fade-in {
  @apply opacity-0 animate-pulse;
  animation: fadeIn 0.2s ease-in-out forwards;
}

.slide-in {
  @apply transform translate-x-full;
  animation: slideIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
