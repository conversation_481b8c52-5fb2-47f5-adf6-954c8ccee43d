"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/firebase-config */ \"./lib/firebase-config.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _VoiceCommands__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./VoiceCommands */ \"./components/VoiceCommands.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckCircle2,ChevronDown,Circle,Clock,FolderOpen,LogOut,MessageCircle,Moon,PlayCircle,Plus,Settings,Sun,Target,User,Zap!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,Calendar,CheckCircle2,ChevronDown,Circle,Clock,FolderOpen,LogOut,MessageCircle,Moon,PlayCircle,Plus,Settings,Sun,Target,User,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Sidebar() {\n    _s();\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    const { getTaskStats, currentProject, projects, setCurrentProject, addProject } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore)();\n    const { theme, toggleTheme, setShowCalendar, setShowAnalytics, setAiChatOpen } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_4__.useUIStore)();\n    const [showAddTask, setShowAddTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewProject, setShowNewProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProjectName, setNewProjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTask, setNewTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        priority: \"medium\",\n        deadline: \"\"\n    });\n    const stats = getTaskStats();\n    const handleLogout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_3__.auth);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Logged out successfully\");\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Failed to logout\");\n        }\n    };\n    const handleAddTask = ()=>{\n        if (!newTask.title.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Please enter a task title\");\n            return;\n        }\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore.getState().addTask({\n            ...newTask,\n            status: \"todo\",\n            project: currentProject\n        });\n        setNewTask({\n            title: \"\",\n            description: \"\",\n            priority: \"medium\",\n            deadline: \"\"\n        });\n        setShowAddTask(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Task added successfully\");\n    };\n    const handleAddProject = ()=>{\n        if (!newProjectName.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Please enter a project name\");\n            return;\n        }\n        if (projects.includes(newProjectName)) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"Project already exists\");\n            return;\n        }\n        addProject(newProjectName);\n        setCurrentProject(newProjectName);\n        setNewProjectName(\"\");\n        setShowNewProject(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].success(\"Project created successfully\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-80 bg-white dark:bg-gray-900 border-r border-gray-100 dark:border-gray-800 flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-100 dark:border-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Zap, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 90,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: \"FocusFlow AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: \"Productivity Companion\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: toggleTheme,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\",\n                                        children: theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Moon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 36\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Sun, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 67\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.User, {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white truncate\",\n                                                        children: (user === null || user === void 0 ? void 0 : user.displayName) || \"Welcome User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 truncate\",\n                                                        children: user === null || user === void 0 ? void 0 : user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                        children: \"Project\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowNewProject(true),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Plus, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: currentProject,\n                                onChange: (e)=>setCurrentProject(e.target.value),\n                                className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: project,\n                                        children: project === \"default\" ? \"Personal\" : project\n                                    }, project, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900 dark:text-white mb-4\",\n                                children: \"Task Overview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Total\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: stats.completed\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-yellow-600\",\n                                                children: stats.inProgress\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"To Do\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-600\",\n                                                children: stats.todo\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowAddTask(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"default\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Plus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add Task\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setAiChatOpen(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.MessageCircle, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Talk to Assistant\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowCalendar(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"ghost\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Calendar, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Calendar View\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: ()=>setShowAnalytics(true),\n                                        className: \"w-full justify-start\",\n                                        variant: \"ghost\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.BarChart3, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Analytics\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VoiceCommands__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: toggleTheme,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Moon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 36\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.Sun, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 67\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleLogout,\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-red-600 hover:text-red-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckCircle2_ChevronDown_Circle_Clock_FolderOpen_LogOut_MessageCircle_Moon_PlayCircle_Plus_Settings_Sun_Target_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__.LogOut, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                isOpen: showAddTask,\n                onClose: ()=>setShowAddTask(false),\n                title: \"Add New Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                    value: newTask.title,\n                                    onChange: (e)=>setNewTask({\n                                            ...newTask,\n                                            title: e.target.value\n                                        }),\n                                    placeholder: \"Enter task title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newTask.description,\n                                    onChange: (e)=>setNewTask({\n                                            ...newTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3,\n                                    placeholder: \"Enter task description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: newTask.priority,\n                                            onChange: (e)=>setNewTask({\n                                                    ...newTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                            type: \"date\",\n                                            value: newTask.deadline,\n                                            onChange: (e)=>setNewTask({\n                                                    ...newTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowAddTask(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleAddTask,\n                                    children: \"Add Task\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n                isOpen: showNewProject,\n                onClose: ()=>setShowNewProject(false),\n                title: \"Create New Project\",\n                size: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Project Name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                    value: newProjectName,\n                                    onChange: (e)=>setNewProjectName(e.target.value),\n                                    placeholder: \"Enter project name\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowNewProject(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: handleAddProject,\n                                    children: \"Create Project\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\Sidebar.js\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"K9ayOq3O4A6NWMvNVNHI1a4q3no=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_4__.useUIStore\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n"));

/***/ })

});