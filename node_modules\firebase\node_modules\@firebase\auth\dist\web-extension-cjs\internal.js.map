{"version": 3, "file": "internal.js", "sources": ["../../src/model/enum_maps.ts", "../../src/platform_browser/persistence/browser.ts", "../../src/platform_browser/persistence/local_storage.ts", "../../src/platform_browser/persistence/cookie_storage.ts", "../../src/platform_browser/persistence/session_storage.ts", "../../src/platform_browser/recaptcha/recaptcha_loader.ts", "../../src/platform_browser/recaptcha/recaptcha_verifier.ts", "../../src/platform_browser/strategies/phone.ts", "../../src/platform_browser/providers/phone.ts", "../../src/core/util/resolver.ts", "../../src/core/strategies/idp.ts", "../../src/core/strategies/abstract_popup_redirect_operation.ts", "../../src/platform_browser/strategies/popup.ts", "../../src/core/strategies/redirect.ts", "../../src/platform_browser/strategies/redirect.ts", "../../src/core/auth/auth_event_manager.ts", "../../src/api/project_config/get_project_config.ts", "../../src/core/util/validate_origin.ts", "../../src/platform_browser/iframe/gapi.ts", "../../src/platform_browser/iframe/iframe.ts", "../../src/platform_browser/util/popup.ts", "../../src/core/util/handler.ts", "../../src/platform_browser/popup_redirect.ts", "../../src/platform_browser/mfa/assertions/phone.ts", "../../src/platform_browser/index.ts", "../../src/platform_cordova/plugins.ts", "../../src/platform_cordova/popup_redirect/utils.ts", "../../src/platform_cordova/popup_redirect/events.ts", "../../src/platform_cordova/popup_redirect/popup_redirect.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An enum of factors that may be used for multifactor authentication.\n *\n * @public\n */\nexport const FactorId = {\n  /** Phone as second factor */\n  PHONE: 'phone',\n  TOTP: 'totp'\n} as const;\n\n/**\n * Enumeration of supported providers.\n *\n * @public\n */\nexport const ProviderId = {\n  /** Facebook provider ID */\n  FACEBOOK: 'facebook.com',\n  /** GitHub provider ID */\n  GITHUB: 'github.com',\n  /** Google provider ID */\n  GOOGLE: 'google.com',\n  /** Password provider */\n  PASSWORD: 'password',\n  /** Phone provider */\n  PHONE: 'phone',\n  /** Twitter provider ID */\n  TWITTER: 'twitter.com'\n} as const;\n\n/**\n * Enumeration of supported sign-in methods.\n *\n * @public\n */\nexport const SignInMethod = {\n  /** Email link sign in method */\n  EMAIL_LINK: 'emailLink',\n  /** Email/password sign in method */\n  EMAIL_PASSWORD: 'password',\n  /** Facebook sign in method */\n  FACEBOOK: 'facebook.com',\n  /** GitHub sign in method */\n  GITHUB: 'github.com',\n  /** Google sign in method */\n  GOOGLE: 'google.com',\n  /** Phone sign in method */\n  PHONE: 'phone',\n  /** Twitter sign in method */\n  TWITTER: 'twitter.com'\n} as const;\n\n/**\n * Enumeration of supported operation types.\n *\n * @public\n */\nexport const OperationType = {\n  /** Operation involving linking an additional provider to an already signed-in user. */\n  LINK: 'link',\n  /** Operation involving using a provider to reauthenticate an already signed-in user. */\n  REAUTHENTICATE: 'reauthenticate',\n  /** Operation involving signing in a user. */\n  SIGN_IN: 'signIn'\n} as const;\n\n/**\n * An enumeration of the possible email action types.\n *\n * @public\n */\nexport const ActionCodeOperation = {\n  /** The email link sign-in action. */\n  EMAIL_SIGNIN: 'EMAIL_SIGNIN',\n  /** The password reset action. */\n  PASSWORD_RESET: 'PASSWORD_RESET',\n  /** The email revocation action. */\n  RECOVER_EMAIL: 'RECOVER_EMAIL',\n  /** The revert second factor addition email action. */\n  REVERT_SECOND_FACTOR_ADDITION: 'REVERT_SECOND_FACTOR_ADDITION',\n  /** The revert second factor addition email action. */\n  VERIFY_AND_CHANGE_EMAIL: 'VERIFY_AND_CHANGE_EMAIL',\n  /** The email verification action. */\n  VERIFY_EMAIL: 'VERIFY_EMAIL'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  PersistenceValue,\n  STORAGE_AVAILABLE_KEY,\n  PersistenceType\n} from '../../core/persistence';\n\n// There are two different browser persistence types: local and session.\n// Both have the same implementation but use a different underlying storage\n// object.\n\nexport abstract class BrowserPersistenceClass {\n  protected constructor(\n    protected readonly storageRetriever: () => Storage,\n    readonly type: PersistenceType\n  ) {}\n\n  _isAvailable(): Promise<boolean> {\n    try {\n      if (!this.storage) {\n        return Promise.resolve(false);\n      }\n      this.storage.setItem(STORAGE_AVAILABLE_KEY, '1');\n      this.storage.removeItem(STORAGE_AVAILABLE_KEY);\n      return Promise.resolve(true);\n    } catch {\n      return Promise.resolve(false);\n    }\n  }\n\n  _set(key: string, value: PersistenceValue): Promise<void> {\n    this.storage.setItem(key, JSON.stringify(value));\n    return Promise.resolve();\n  }\n\n  _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const json = this.storage.getItem(key);\n    return Promise.resolve(json ? JSON.parse(json) : null);\n  }\n\n  _remove(key: string): Promise<void> {\n    this.storage.removeItem(key);\n    return Promise.resolve();\n  }\n\n  protected get storage(): Storage {\n    return this.storageRetriever();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport { _isMobileBrowser, _isIE10 } from '../../core/util/browser';\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\n// The polling period in case events are not supported\nexport const _POLLING_INTERVAL_MS = 1000;\n\n// The IE 10 localStorage cross tab synchronization delay in milliseconds\nconst IE10_LOCAL_STORAGE_SYNC_DELAY = 10;\n\nclass BrowserLocalPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'LOCAL' = 'LOCAL';\n\n  constructor() {\n    super(() => window.localStorage, PersistenceType.LOCAL);\n  }\n\n  private readonly boundEventHandler = (\n    event: StorageEvent,\n    poll?: boolean\n  ): void => this.onStorageEvent(event, poll);\n  private readonly listeners: Record<string, Set<StorageEventListener>> = {};\n  private readonly localCache: Record<string, string | null> = {};\n  // setTimeout return value is platform specific\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private pollTimer: any | null = null;\n\n  // Whether to use polling instead of depending on window events\n  private readonly fallbackToPolling = _isMobileBrowser();\n  readonly _shouldAllowMigration = true;\n\n  private forAllChangedKeys(\n    cb: (key: string, oldValue: string | null, newValue: string | null) => void\n  ): void {\n    // Check all keys with listeners on them.\n    for (const key of Object.keys(this.listeners)) {\n      // Get value from localStorage.\n      const newValue = this.storage.getItem(key);\n      const oldValue = this.localCache[key];\n      // If local map value does not match, trigger listener with storage event.\n      // Differentiate this simulated event from the real storage event.\n      if (newValue !== oldValue) {\n        cb(key, oldValue, newValue);\n      }\n    }\n  }\n\n  private onStorageEvent(event: StorageEvent, poll = false): void {\n    // Key would be null in some situations, like when localStorage is cleared\n    if (!event.key) {\n      this.forAllChangedKeys(\n        (key: string, _oldValue: string | null, newValue: string | null) => {\n          this.notifyListeners(key, newValue);\n        }\n      );\n      return;\n    }\n\n    const key = event.key;\n\n    // Check the mechanism how this event was detected.\n    // The first event will dictate the mechanism to be used.\n    if (poll) {\n      // Environment detects storage changes via polling.\n      // Remove storage event listener to prevent possible event duplication.\n      this.detachListener();\n    } else {\n      // Environment detects storage changes via storage event listener.\n      // Remove polling listener to prevent possible event duplication.\n      this.stopPolling();\n    }\n\n    const triggerListeners = (): void => {\n      // Keep local map up to date in case storage event is triggered before\n      // poll.\n      const storedValue = this.storage.getItem(key);\n      if (!poll && this.localCache[key] === storedValue) {\n        // Real storage event which has already been detected, do nothing.\n        // This seems to trigger in some IE browsers for some reason.\n        return;\n      }\n      this.notifyListeners(key, storedValue);\n    };\n\n    const storedValue = this.storage.getItem(key);\n    if (\n      _isIE10() &&\n      storedValue !== event.newValue &&\n      event.newValue !== event.oldValue\n    ) {\n      // IE 10 has this weird bug where a storage event would trigger with the\n      // correct key, oldValue and newValue but localStorage.getItem(key) does\n      // not yield the updated value until a few milliseconds. This ensures\n      // this recovers from that situation.\n      setTimeout(triggerListeners, IE10_LOCAL_STORAGE_SYNC_DELAY);\n    } else {\n      triggerListeners();\n    }\n  }\n\n  private notifyListeners(key: string, value: string | null): void {\n    this.localCache[key] = value;\n    const listeners = this.listeners[key];\n    if (listeners) {\n      for (const listener of Array.from(listeners)) {\n        listener(value ? JSON.parse(value) : value);\n      }\n    }\n  }\n\n  private startPolling(): void {\n    this.stopPolling();\n\n    this.pollTimer = setInterval(() => {\n      this.forAllChangedKeys(\n        (key: string, oldValue: string | null, newValue: string | null) => {\n          this.onStorageEvent(\n            new StorageEvent('storage', {\n              key,\n              oldValue,\n              newValue\n            }),\n            /* poll */ true\n          );\n        }\n      );\n    }, _POLLING_INTERVAL_MS);\n  }\n\n  private stopPolling(): void {\n    if (this.pollTimer) {\n      clearInterval(this.pollTimer);\n      this.pollTimer = null;\n    }\n  }\n\n  private attachListener(): void {\n    window.addEventListener('storage', this.boundEventHandler);\n  }\n\n  private detachListener(): void {\n    window.removeEventListener('storage', this.boundEventHandler);\n  }\n\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (Object.keys(this.listeners).length === 0) {\n      // Whether browser can detect storage event when it had already been pushed to the background.\n      // This may happen in some mobile browsers. A localStorage change in the foreground window\n      // will not be detected in the background window via the storage event.\n      // This was detected in iOS 7.x mobile browsers\n      if (this.fallbackToPolling) {\n        this.startPolling();\n      } else {\n        this.attachListener();\n      }\n    }\n    if (!this.listeners[key]) {\n      this.listeners[key] = new Set();\n      // Populate the cache to avoid spuriously triggering on first poll.\n      this.localCache[key] = this.storage.getItem(key);\n    }\n    this.listeners[key].add(listener);\n  }\n\n  _removeListener(key: string, listener: StorageEventListener): void {\n    if (this.listeners[key]) {\n      this.listeners[key].delete(listener);\n\n      if (this.listeners[key].size === 0) {\n        delete this.listeners[key];\n      }\n    }\n\n    if (Object.keys(this.listeners).length === 0) {\n      this.detachListener();\n      this.stopPolling();\n    }\n  }\n\n  // Update local cache on base operations:\n\n  async _set(key: string, value: PersistenceValue): Promise<void> {\n    await super._set(key, value);\n    this.localCache[key] = JSON.stringify(value);\n  }\n\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    const value = await super._get<T>(key);\n    this.localCache[key] = JSON.stringify(value);\n    return value;\n  }\n\n  async _remove(key: string): Promise<void> {\n    await super._remove(key);\n    delete this.localCache[key];\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `LOCAL` using `localStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserLocalPersistence: Persistence = BrowserLocalPersistence;\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\nimport type { CookieChangeEvent } from 'cookie-store';\n\nconst POLLING_INTERVAL_MS = 1_000;\n\nimport {\n  PersistenceInternal,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener\n} from '../../core/persistence';\n\n// Pull a cookie value from document.cookie\nfunction getDocumentCookie(name: string): string | null {\n  const escapedName = name.replace(/[\\\\^$.*+?()[\\]{}|]/g, '\\\\$&');\n  const matcher = RegExp(`${escapedName}=([^;]+)`);\n  return document.cookie.match(matcher)?.[1] ?? null;\n}\n\n// Produce a sanitized cookie name from the persistence key\nfunction getCookieName(key: string): string {\n  // __HOST- doesn't work in localhost https://issues.chromium.org/issues/40196122 but it has\n  // desirable security properties, so lets use a different cookie name while in dev-mode.\n  // Already checked isSecureContext in _isAvailable, so if it's http we're hitting local.\n  const isDevMode = window.location.protocol === 'http:';\n  return `${isDevMode ? '__dev_' : '__HOST-'}FIREBASE_${key.split(':')[3]}`;\n}\n\nexport class CookiePersistence implements PersistenceInternal {\n  static type: 'COOKIE' = 'COOKIE';\n  readonly type = PersistenceType.COOKIE;\n  listenerUnsubscribes: Map<StorageEventListener, () => void> = new Map();\n\n  // used to get the URL to the backend to proxy to\n  _getFinalTarget(originalUrl: string): URL | string {\n    if (typeof window === undefined) {\n      return originalUrl;\n    }\n    const url = new URL(`${window.location.origin}/__cookies__`);\n    url.searchParams.set('finalTarget', originalUrl);\n    return url;\n  }\n\n  // To be a usable persistence method in a chain browserCookiePersistence ensures that\n  // prerequisites have been met, namely that we're in a secureContext, navigator and document are\n  // available and cookies are enabled. Not all UAs support these method, so fallback accordingly.\n  async _isAvailable(): Promise<boolean> {\n    if (typeof isSecureContext === 'boolean' && !isSecureContext) {\n      return false;\n    }\n    if (typeof navigator === 'undefined' || typeof document === 'undefined') {\n      return false;\n    }\n    return navigator.cookieEnabled ?? true;\n  }\n\n  // Set should be a noop as we expect middleware to handle this\n  async _set(_key: string, _value: PersistenceValue): Promise<void> {\n    return;\n  }\n\n  // Attempt to get the cookie from cookieStore, fallback to document.cookie\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    if (!this._isAvailable()) {\n      return null;\n    }\n    const name = getCookieName(key);\n    if (window.cookieStore) {\n      const cookie = await window.cookieStore.get(name);\n      return cookie?.value as T;\n    }\n    return getDocumentCookie(name) as T;\n  }\n\n  // Log out by overriding the idToken with a sentinel value of \"\"\n  async _remove(key: string): Promise<void> {\n    if (!this._isAvailable()) {\n      return;\n    }\n    // To make sure we don't hit signout over and over again, only do this operation if we need to\n    // with the logout sentinel value of \"\" this can cause race conditions. Unnecessary set-cookie\n    // headers will reduce CDN hit rates too.\n    const existingValue = await this._get(key);\n    if (!existingValue) {\n      return;\n    }\n    const name = getCookieName(key);\n    document.cookie = `${name}=;Max-Age=34560000;Partitioned;Secure;SameSite=Strict;Path=/;Priority=High`;\n    await fetch(`/__cookies__`, { method: 'DELETE' }).catch(() => undefined);\n  }\n\n  // Listen for cookie changes, both cookieStore and fallback to polling document.cookie\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (!this._isAvailable()) {\n      return;\n    }\n    const name = getCookieName(key);\n    if (window.cookieStore) {\n      const cb = ((event: CookieChangeEvent): void => {\n        const changedCookie = event.changed.find(\n          change => change.name === name\n        );\n        if (changedCookie) {\n          listener(changedCookie.value as PersistenceValue);\n        }\n        const deletedCookie = event.deleted.find(\n          change => change.name === name\n        );\n        if (deletedCookie) {\n          listener(null);\n        }\n      }) as EventListener;\n      const unsubscribe = (): void =>\n        window.cookieStore.removeEventListener('change', cb);\n      this.listenerUnsubscribes.set(listener, unsubscribe);\n      return window.cookieStore.addEventListener('change', cb as EventListener);\n    }\n    let lastValue = getDocumentCookie(name);\n    const interval = setInterval(() => {\n      const currentValue = getDocumentCookie(name);\n      if (currentValue !== lastValue) {\n        listener(currentValue as PersistenceValue | null);\n        lastValue = currentValue;\n      }\n    }, POLLING_INTERVAL_MS);\n    const unsubscribe = (): void => clearInterval(interval);\n    this.listenerUnsubscribes.set(listener, unsubscribe);\n  }\n\n  _removeListener(_key: string, listener: StorageEventListener): void {\n    const unsubscribe = this.listenerUnsubscribes.get(listener);\n    if (!unsubscribe) {\n      return;\n    }\n    unsubscribe();\n    this.listenerUnsubscribes.delete(listener);\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `COOKIE`, for use on the client side in\n * applications leveraging hybrid rendering and middleware.\n *\n * @remarks This persistence method requires companion middleware to function, such as that provided\n * by {@link https://firebaseopensource.com/projects/firebaseextended/reactfire/ | ReactFire} for\n * NextJS.\n * @beta\n */\nexport const browserCookiePersistence: Persistence = CookiePersistence;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\n\nimport {\n  PersistenceInternal as InternalPersistence,\n  PersistenceType,\n  StorageEventListener\n} from '../../core/persistence';\nimport { BrowserPersistenceClass } from './browser';\n\nclass BrowserSessionPersistence\n  extends BrowserPersistenceClass\n  implements InternalPersistence\n{\n  static type: 'SESSION' = 'SESSION';\n\n  constructor() {\n    super(() => window.sessionStorage, PersistenceType.SESSION);\n  }\n\n  _addListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n\n  _removeListener(_key: string, _listener: StorageEventListener): void {\n    // Listeners are not supported for session storage since it cannot be shared across windows\n    return;\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of `SESSION` using `sessionStorage`\n * for the underlying storage.\n *\n * @public\n */\nexport const browserSessionPersistence: Persistence = BrowserSessionPersistence;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystring } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as jsHelpers from '../load_js';\nimport { Recaptcha, isV2 } from './recaptcha';\nimport { MockReCaptcha } from './recaptcha_mock';\n\n// ReCaptch<PERSON> will load using the same callback, so the callback function needs\n// to be kept around\nexport const _JSLOAD_CALLBACK = jsHelpers._generateCallbackName('rcb');\nconst NETWORK_TIMEOUT_DELAY = new Delay(30000, 60000);\n\n/**\n * We need to mark this interface as internal explicitly to exclude it in the public typings, because\n * it references AuthInternal which has a circular dependency with UserInternal.\n *\n * @internal\n */\nexport interface ReCaptchaLoader {\n  load(auth: AuthInternal, hl?: string): Promise<Recaptcha>;\n  clearedOneInstance(): void;\n}\n\n/**\n * Loader for the GReCaptcha library. There should only ever be one of this.\n */\nexport class ReCaptchaLoaderImpl implements ReCaptchaLoader {\n  private hostLanguage = '';\n  private counter = 0;\n  /**\n   * Check for `render()` method. `window.grecaptcha` will exist if the Enterprise\n   * version of the ReCAPTCHA script was loaded by someone else (e.g. App Check) but\n   * `window.grecaptcha.render()` will not. Another load will add it.\n   */\n  private readonly librarySeparatelyLoaded = !!_window().grecaptcha?.render;\n\n  load(auth: AuthInternal, hl = ''): Promise<Recaptcha> {\n    _assert(isHostLanguageValid(hl), auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    if (this.shouldResolveImmediately(hl) && isV2(_window().grecaptcha)) {\n      return Promise.resolve(_window().grecaptcha! as Recaptcha);\n    }\n    return new Promise<Recaptcha>((resolve, reject) => {\n      const networkTimeout = _window().setTimeout(() => {\n        reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n      }, NETWORK_TIMEOUT_DELAY.get());\n\n      _window()[_JSLOAD_CALLBACK] = () => {\n        _window().clearTimeout(networkTimeout);\n        delete _window()[_JSLOAD_CALLBACK];\n\n        const recaptcha = _window().grecaptcha as Recaptcha;\n\n        if (!recaptcha || !isV2(recaptcha)) {\n          reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n          return;\n        }\n\n        // Wrap the recaptcha render function so that we know if the developer has\n        // called it separately\n        const render = recaptcha.render;\n        recaptcha.render = (container, params) => {\n          const widgetId = render(container, params);\n          this.counter++;\n          return widgetId;\n        };\n\n        this.hostLanguage = hl;\n        resolve(recaptcha);\n      };\n\n      const url = `${jsHelpers._recaptchaV2ScriptUrl()}?${querystring({\n        onload: _JSLOAD_CALLBACK,\n        render: 'explicit',\n        hl\n      })}`;\n\n      jsHelpers._loadJS(url).catch(() => {\n        clearTimeout(networkTimeout);\n        reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n      });\n    });\n  }\n\n  clearedOneInstance(): void {\n    this.counter--;\n  }\n\n  private shouldResolveImmediately(hl: string): boolean {\n    // We can resolve immediately if:\n    //   • grecaptcha is already defined AND (\n    //     1. the requested language codes are the same OR\n    //     2. there exists already a ReCaptcha on the page\n    //     3. the library was already loaded by the app\n    // In cases (2) and (3), we _can't_ reload as it would break the recaptchas\n    // that are already in the page\n    return (\n      !!_window().grecaptcha?.render &&\n      (hl === this.hostLanguage ||\n        this.counter > 0 ||\n        this.librarySeparatelyLoaded)\n    );\n  }\n}\n\nfunction isHostLanguageValid(hl: string): boolean {\n  return hl.length <= 6 && /^\\s*[a-zA-Z0-9\\-]*\\s*$/.test(hl);\n}\n\nexport class MockReCaptchaLoaderImpl implements ReCaptchaLoader {\n  async load(auth: AuthInternal): Promise<Recaptcha> {\n    return new MockReCaptcha(auth);\n  }\n\n  clearedOneInstance(): void {}\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Auth, RecaptchaParameters } from '../../model/public_types';\nimport { getRecaptchaParams } from '../../api/authentication/recaptcha';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport { _isHttpOrHttps } from '../../core/util/location';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport { _isWorker } from '../util/worker';\nimport { Recaptcha } from './recaptcha';\nimport {\n  MockReCaptchaLoaderImpl,\n  ReCaptchaLoader,\n  ReCaptchaLoaderImpl\n} from './recaptcha_loader';\n\nexport const RECAPTCHA_VERIFIER_TYPE = 'recaptcha';\n\nconst DEFAULT_PARAMS: RecaptchaParameters = {\n  theme: 'light',\n  type: 'image'\n};\n\ntype TokenCallback = (token: string) => void;\n\n/**\n * An {@link https://www.google.com/recaptcha/ | reCAPTCHA}-based application verifier.\n *\n * @remarks\n * `RecaptchaVerifier` does not work in a Node.js environment.\n *\n * @public\n */\nexport class RecaptchaVerifier implements ApplicationVerifierInternal {\n  /**\n   * The application verifier type.\n   *\n   * @remarks\n   * For a reCAPTCHA verifier, this is 'recaptcha'.\n   */\n  readonly type = RECAPTCHA_VERIFIER_TYPE;\n  private destroyed = false;\n  private widgetId: number | null = null;\n  private readonly container: HTMLElement;\n  private readonly isInvisible: boolean;\n  private readonly tokenChangeListeners = new Set<TokenCallback>();\n  private renderPromise: Promise<number> | null = null;\n  private readonly auth: AuthInternal;\n\n  /** @internal */\n  readonly _recaptchaLoader: ReCaptchaLoader;\n  private recaptcha: Recaptcha | null = null;\n\n  /**\n   * @param authExtern - The corresponding Firebase {@link Auth} instance.\n   *\n   * @param containerOrId - The reCAPTCHA container parameter.\n   *\n   * @remarks\n   * This has different meaning depending on whether the reCAPTCHA is hidden or visible. For a\n   * visible reCAPTCHA the container must be empty. If a string is used, it has to correspond to\n   * an element ID. The corresponding element must also must be in the DOM at the time of\n   * initialization.\n   *\n   * @param parameters - The optional reCAPTCHA parameters.\n   *\n   * @remarks\n   * Check the reCAPTCHA docs for a comprehensive list. All parameters are accepted except for\n   * the sitekey. Firebase Auth backend provisions a reCAPTCHA for each project and will\n   * configure this upon rendering. For an invisible reCAPTCHA, a size key must have the value\n   * 'invisible'.\n   */\n  constructor(\n    authExtern: Auth,\n    containerOrId: HTMLElement | string,\n    private readonly parameters: RecaptchaParameters = {\n      ...DEFAULT_PARAMS\n    }\n  ) {\n    this.auth = _castAuth(authExtern);\n    this.isInvisible = this.parameters.size === 'invisible';\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n    const container =\n      typeof containerOrId === 'string'\n        ? document.getElementById(containerOrId)\n        : containerOrId;\n    _assert(container, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    this.container = container;\n    this.parameters.callback = this.makeTokenCallback(this.parameters.callback);\n\n    this._recaptchaLoader = this.auth.settings.appVerificationDisabledForTesting\n      ? new MockReCaptchaLoaderImpl()\n      : new ReCaptchaLoaderImpl();\n\n    this.validateStartingState();\n    // TODO: Figure out if sdk version is needed\n  }\n\n  /**\n   * Waits for the user to solve the reCAPTCHA and resolves with the reCAPTCHA token.\n   *\n   * @returns A Promise for the reCAPTCHA token.\n   */\n  async verify(): Promise<string> {\n    this.assertNotDestroyed();\n    const id = await this.render();\n    const recaptcha = this.getAssertedRecaptcha();\n\n    const response = recaptcha.getResponse(id);\n    if (response) {\n      return response;\n    }\n\n    return new Promise<string>(resolve => {\n      const tokenChange = (token: string): void => {\n        if (!token) {\n          return; // Ignore token expirations.\n        }\n        this.tokenChangeListeners.delete(tokenChange);\n        resolve(token);\n      };\n\n      this.tokenChangeListeners.add(tokenChange);\n      if (this.isInvisible) {\n        recaptcha.execute(id);\n      }\n    });\n  }\n\n  /**\n   * Renders the reCAPTCHA widget on the page.\n   *\n   * @returns A Promise that resolves with the reCAPTCHA widget ID.\n   */\n  render(): Promise<number> {\n    try {\n      this.assertNotDestroyed();\n    } catch (e) {\n      // This method returns a promise. Since it's not async (we want to return the\n      // _same_ promise if rendering is still occurring), the API surface should\n      // reject with the error rather than just throw\n      return Promise.reject(e);\n    }\n\n    if (this.renderPromise) {\n      return this.renderPromise;\n    }\n\n    this.renderPromise = this.makeRenderPromise().catch(e => {\n      this.renderPromise = null;\n      throw e;\n    });\n\n    return this.renderPromise;\n  }\n\n  /** @internal */\n  _reset(): void {\n    this.assertNotDestroyed();\n    if (this.widgetId !== null) {\n      this.getAssertedRecaptcha().reset(this.widgetId);\n    }\n  }\n\n  /**\n   * Clears the reCAPTCHA widget from the page and destroys the instance.\n   */\n  clear(): void {\n    this.assertNotDestroyed();\n    this.destroyed = true;\n    this._recaptchaLoader.clearedOneInstance();\n    if (!this.isInvisible) {\n      this.container.childNodes.forEach(node => {\n        this.container.removeChild(node);\n      });\n    }\n  }\n\n  private validateStartingState(): void {\n    _assert(!this.parameters.sitekey, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n    _assert(\n      this.isInvisible || !this.container.hasChildNodes(),\n      this.auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n  }\n\n  private makeTokenCallback(\n    existing: TokenCallback | string | undefined\n  ): TokenCallback {\n    return token => {\n      this.tokenChangeListeners.forEach(listener => listener(token));\n      if (typeof existing === 'function') {\n        existing(token);\n      } else if (typeof existing === 'string') {\n        const globalFunc = _window()[existing];\n        if (typeof globalFunc === 'function') {\n          globalFunc(token);\n        }\n      }\n    };\n  }\n\n  private assertNotDestroyed(): void {\n    _assert(!this.destroyed, this.auth, AuthErrorCode.INTERNAL_ERROR);\n  }\n\n  private async makeRenderPromise(): Promise<number> {\n    await this.init();\n    if (!this.widgetId) {\n      let container = this.container;\n      if (!this.isInvisible) {\n        const guaranteedEmpty = document.createElement('div');\n        container.appendChild(guaranteedEmpty);\n        container = guaranteedEmpty;\n      }\n\n      this.widgetId = this.getAssertedRecaptcha().render(\n        container,\n        this.parameters\n      );\n    }\n\n    return this.widgetId;\n  }\n\n  private async init(): Promise<void> {\n    _assert(\n      _isHttpOrHttps() && !_isWorker(),\n      this.auth,\n      AuthErrorCode.INTERNAL_ERROR\n    );\n\n    await domReady();\n    this.recaptcha = await this._recaptchaLoader.load(\n      this.auth,\n      this.auth.languageCode || undefined\n    );\n\n    const siteKey = await getRecaptchaParams(this.auth);\n    _assert(siteKey, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    this.parameters.sitekey = siteKey;\n  }\n\n  private getAssertedRecaptcha(): Recaptcha {\n    _assert(this.recaptcha, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return this.recaptcha;\n  }\n}\n\nfunction domReady(): Promise<void> {\n  let resolver: (() => void) | null = null;\n  return new Promise<void>(resolve => {\n    if (document.readyState === 'complete') {\n      resolve();\n      return;\n    }\n\n    // Document not ready, wait for load before resolving.\n    // Save resolver, so we can remove listener in case it was externally\n    // cancelled.\n    resolver = () => resolve();\n    window.addEventListener('load', resolver);\n  }).catch(e => {\n    if (resolver) {\n      window.removeEventListener('load', resolver);\n    }\n\n    throw e;\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ApplicationVerifier,\n  Auth,\n  ConfirmationResult,\n  PhoneInfoOptions,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport {\n  startEnrollPhoneMfa,\n  StartPhoneMfaEnrollmentRequest,\n  StartPhoneMfaEnrollmentResponse\n} from '../../api/account_management/mfa';\nimport {\n  startSignInPhoneMfa,\n  StartPhoneMfaSignInRequest,\n  StartPhoneMfaSignInResponse\n} from '../../api/authentication/mfa';\nimport {\n  sendPhoneVerificationCode,\n  SendPhoneVerificationCodeRequest,\n  SendPhoneVerificationCodeResponse\n} from '../../api/authentication/sms';\nimport {\n  RecaptchaActionName,\n  RecaptchaClientType,\n  RecaptchaAuthProvider\n} from '../../api';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assertLinkedStatus, _link } from '../../core/user/link_unlink';\nimport {\n  _assert,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  linkWithCredential,\n  reauthenticateWithCredential,\n  signInWithCredential\n} from '../../core/strategies/credential';\nimport {\n  MultiFactorSessionImpl,\n  MultiFactorSessionType\n} from '../../mfa/mfa_session';\nimport { UserInternal } from '../../model/user';\nimport { RECAPTCHA_VERIFIER_TYPE } from '../recaptcha/recaptcha_verifier';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { getModularInstance } from '@firebase/util';\nimport { ProviderId } from '../../model/enums';\nimport {\n  FAKE_TOKEN,\n  handleRecaptchaFlow,\n  _initializeRecaptchaConfig\n} from '../recaptcha/recaptcha_enterprise_verifier';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\ninterface OnConfirmationCallback {\n  (credential: PhoneAuthCredential): Promise<UserCredential>;\n}\n\nclass ConfirmationResultImpl implements ConfirmationResult {\n  constructor(\n    readonly verificationId: string,\n    private readonly onConfirmation: OnConfirmationCallback\n  ) {}\n\n  confirm(verificationCode: string): Promise<UserCredential> {\n    const authCredential = PhoneAuthCredential._fromVerification(\n      this.verificationId,\n      verificationCode\n    );\n    return this.onConfirmation(authCredential);\n  }\n}\n\n/**\n * Asynchronously signs in using a phone number.\n *\n * @remarks\n * This method sends a code via SMS to the given\n * phone number, and returns a {@link ConfirmationResult}. After the user\n * provides the code sent to their phone, call {@link ConfirmationResult.confirm}\n * with the code to sign the user in.\n *\n * For abuse prevention, this method requires a {@link ApplicationVerifier}.\n * This SDK includes an implementation based on reCAPTCHA v2, {@link RecaptchaVerifier}.\n * This function can work on other platforms that do not support the\n * {@link RecaptchaVerifier} (like React Native), but you need to use a\n * third-party {@link ApplicationVerifier} implementation.\n *\n * If you've enabled project-level reCAPTCHA Enterprise bot protection in\n * Enforce mode, you can omit the {@link ApplicationVerifier}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container');\n * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n * // Obtain a verificationCode from the user.\n * const credential = await confirmationResult.confirm(verificationCode);\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function signInWithPhoneNumber(\n  auth: Auth,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const verificationId = await _verifyPhoneNumber(\n    authInternal,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    signInWithCredential(authInternal, cred)\n  );\n}\n\n/**\n * Links the user account with the given phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function linkWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  await _assertLinkedStatus(false, userInternal, ProviderId.PHONE);\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    linkWithCredential(userInternal, cred)\n  );\n}\n\n/**\n * Re-authenticates a user using a fresh phone credential.\n *\n * @remarks\n * Use before operations such as {@link updatePassword} that require tokens from recent sign-in attempts.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function reauthenticateWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    reauthenticateWithCredential(userInternal, cred)\n  );\n}\n\ntype PhoneApiCaller<TRequest, TResponse> = (\n  auth: AuthInternal,\n  request: TRequest\n) => Promise<TResponse>;\n\n/**\n * Returns a verification ID to be used in conjunction with the SMS code that is sent.\n *\n */\nexport async function _verifyPhoneNumber(\n  auth: AuthInternal,\n  options: PhoneInfoOptions | string,\n  verifier?: ApplicationVerifierInternal\n): Promise<string> {\n  if (!auth._getRecaptchaConfig()) {\n    try {\n      await _initializeRecaptchaConfig(auth);\n    } catch (error) {\n      // If an error occurs while fetching the config, there is no way to know the enablement state\n      // of Phone provider, so we proceed with recaptcha V2 verification.\n      // The error is likely \"recaptchaKey undefined\", as reCAPTCHA Enterprise is not\n      // enabled for any provider.\n      console.log(\n        'Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.'\n      );\n    }\n  }\n\n  try {\n    let phoneInfoOptions: PhoneInfoOptions;\n\n    if (typeof options === 'string') {\n      phoneInfoOptions = {\n        phoneNumber: options\n      };\n    } else {\n      phoneInfoOptions = options;\n    }\n\n    if ('session' in phoneInfoOptions) {\n      const session = phoneInfoOptions.session as MultiFactorSessionImpl;\n\n      if ('phoneNumber' in phoneInfoOptions) {\n        _assert(\n          session.type === MultiFactorSessionType.ENROLL,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n\n        const startPhoneMfaEnrollmentRequest: StartPhoneMfaEnrollmentRequest = {\n          idToken: session.credential,\n          phoneEnrollmentInfo: {\n            phoneNumber: phoneInfoOptions.phoneNumber,\n            clientType: RecaptchaClientType.WEB\n          }\n        };\n\n        const startEnrollPhoneMfaActionCallback: PhoneApiCaller<\n          StartPhoneMfaEnrollmentRequest,\n          StartPhoneMfaEnrollmentResponse\n        > = async (\n          authInstance: AuthInternal,\n          request: StartPhoneMfaEnrollmentRequest\n        ) => {\n          // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n          if (request.phoneEnrollmentInfo.captchaResponse === FAKE_TOKEN) {\n            _assert(\n              verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n              authInstance,\n              AuthErrorCode.ARGUMENT_ERROR\n            );\n\n            const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n              authInstance,\n              request,\n              verifier\n            );\n            return startEnrollPhoneMfa(authInstance, requestWithRecaptchaV2);\n          }\n          return startEnrollPhoneMfa(authInstance, request);\n        };\n\n        const startPhoneMfaEnrollmentResponse: Promise<StartPhoneMfaEnrollmentResponse> =\n          handleRecaptchaFlow(\n            auth,\n            startPhoneMfaEnrollmentRequest,\n            RecaptchaActionName.MFA_SMS_ENROLLMENT,\n            startEnrollPhoneMfaActionCallback,\n            RecaptchaAuthProvider.PHONE_PROVIDER\n          );\n\n        const response = await startPhoneMfaEnrollmentResponse.catch(error => {\n          return Promise.reject(error);\n        });\n\n        return response.phoneSessionInfo.sessionInfo;\n      } else {\n        _assert(\n          session.type === MultiFactorSessionType.SIGN_IN,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n        const mfaEnrollmentId =\n          phoneInfoOptions.multiFactorHint?.uid ||\n          phoneInfoOptions.multiFactorUid;\n        _assert(mfaEnrollmentId, auth, AuthErrorCode.MISSING_MFA_INFO);\n\n        const startPhoneMfaSignInRequest: StartPhoneMfaSignInRequest = {\n          mfaPendingCredential: session.credential,\n          mfaEnrollmentId,\n          phoneSignInInfo: {\n            clientType: RecaptchaClientType.WEB\n          }\n        };\n\n        const startSignInPhoneMfaActionCallback: PhoneApiCaller<\n          StartPhoneMfaSignInRequest,\n          StartPhoneMfaSignInResponse\n        > = async (\n          authInstance: AuthInternal,\n          request: StartPhoneMfaSignInRequest\n        ) => {\n          // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n          if (request.phoneSignInInfo.captchaResponse === FAKE_TOKEN) {\n            _assert(\n              verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n              authInstance,\n              AuthErrorCode.ARGUMENT_ERROR\n            );\n\n            const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n              authInstance,\n              request,\n              verifier\n            );\n            return startSignInPhoneMfa(authInstance, requestWithRecaptchaV2);\n          }\n          return startSignInPhoneMfa(authInstance, request);\n        };\n\n        const startPhoneMfaSignInResponse: Promise<StartPhoneMfaSignInResponse> =\n          handleRecaptchaFlow(\n            auth,\n            startPhoneMfaSignInRequest,\n            RecaptchaActionName.MFA_SMS_SIGNIN,\n            startSignInPhoneMfaActionCallback,\n            RecaptchaAuthProvider.PHONE_PROVIDER\n          );\n\n        const response = await startPhoneMfaSignInResponse.catch(error => {\n          return Promise.reject(error);\n        });\n\n        return response.phoneResponseInfo.sessionInfo;\n      }\n    } else {\n      const sendPhoneVerificationCodeRequest: SendPhoneVerificationCodeRequest =\n        {\n          phoneNumber: phoneInfoOptions.phoneNumber,\n          clientType: RecaptchaClientType.WEB\n        };\n\n      const sendPhoneVerificationCodeActionCallback: PhoneApiCaller<\n        SendPhoneVerificationCodeRequest,\n        SendPhoneVerificationCodeResponse\n      > = async (\n        authInstance: AuthInternal,\n        request: SendPhoneVerificationCodeRequest\n      ) => {\n        // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n        if (request.captchaResponse === FAKE_TOKEN) {\n          _assert(\n            verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n            authInstance,\n            AuthErrorCode.ARGUMENT_ERROR\n          );\n\n          const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n            authInstance,\n            request,\n            verifier\n          );\n          return sendPhoneVerificationCode(\n            authInstance,\n            requestWithRecaptchaV2\n          );\n        }\n        return sendPhoneVerificationCode(authInstance, request);\n      };\n\n      const sendPhoneVerificationCodeResponse: Promise<SendPhoneVerificationCodeResponse> =\n        handleRecaptchaFlow(\n          auth,\n          sendPhoneVerificationCodeRequest,\n          RecaptchaActionName.SEND_VERIFICATION_CODE,\n          sendPhoneVerificationCodeActionCallback,\n          RecaptchaAuthProvider.PHONE_PROVIDER\n        );\n\n      const response = await sendPhoneVerificationCodeResponse.catch(error => {\n        return Promise.reject(error);\n      });\n\n      return response.sessionInfo;\n    }\n  } finally {\n    verifier?._reset();\n  }\n}\n\n/**\n * Updates the user's phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * await updatePhoneNumber(user, phoneCredential);\n * ```\n *\n * @param user - The user.\n * @param credential - A credential authenticating the new phone number.\n *\n * @public\n */\nexport async function updatePhoneNumber(\n  user: User,\n  credential: PhoneAuthCredential\n): Promise<void> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  await _link(userInternal, credential);\n}\n\n// Helper function that fetches and injects a reCAPTCHA v2 token into the request.\nexport async function injectRecaptchaV2Token<T extends object>(\n  auth: AuthInternal,\n  request: T,\n  recaptchaV2Verifier: ApplicationVerifierInternal\n): Promise<T> {\n  _assert(\n    recaptchaV2Verifier.type === RECAPTCHA_VERIFIER_TYPE,\n    auth,\n    AuthErrorCode.ARGUMENT_ERROR\n  );\n\n  const recaptchaV2Token = await recaptchaV2Verifier.verify();\n\n  _assert(\n    typeof recaptchaV2Token === 'string',\n    auth,\n    AuthErrorCode.ARGUMENT_ERROR\n  );\n\n  const newRequest = { ...request };\n\n  if ('phoneEnrollmentInfo' in newRequest) {\n    const phoneNumber = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.phoneNumber;\n    const captchaResponse = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.captchaResponse;\n    const clientType = (newRequest as unknown as StartPhoneMfaEnrollmentRequest)\n      .phoneEnrollmentInfo.clientType;\n    const recaptchaVersion = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.recaptchaVersion;\n\n    Object.assign(newRequest, {\n      'phoneEnrollmentInfo': {\n        phoneNumber,\n        recaptchaToken: recaptchaV2Token,\n        captchaResponse,\n        clientType,\n        recaptchaVersion\n      }\n    });\n\n    return newRequest;\n  } else if ('phoneSignInInfo' in newRequest) {\n    const captchaResponse = (\n      newRequest as unknown as StartPhoneMfaSignInRequest\n    ).phoneSignInInfo.captchaResponse;\n    const clientType = (newRequest as unknown as StartPhoneMfaSignInRequest)\n      .phoneSignInInfo.clientType;\n    const recaptchaVersion = (\n      newRequest as unknown as StartPhoneMfaSignInRequest\n    ).phoneSignInInfo.recaptchaVersion;\n\n    Object.assign(newRequest, {\n      'phoneSignInInfo': {\n        recaptchaToken: recaptchaV2Token,\n        captchaResponse,\n        clientType,\n        recaptchaVersion\n      }\n    });\n\n    return newRequest;\n  } else {\n    Object.assign(newRequest, { 'recaptchaToken': recaptchaV2Token });\n    return newRequest;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  PhoneInfoOptions,\n  ApplicationVerifier,\n  UserCredential\n} from '../../model/public_types';\n\nimport { SignInWithPhoneNumberResponse } from '../../api/authentication/sms';\nimport { ApplicationVerifierInternal as ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal as AuthInternal } from '../../model/auth';\nimport { UserCredentialInternal as UserCredentialInternal } from '../../model/user';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { _verifyPhoneNumber } from '../strategies/phone';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthCredential } from '../../core';\nimport { FirebaseError, getModularInstance } from '@firebase/util';\nimport { TaggedWithTokenResponse } from '../../model/id_token';\nimport { ProviderId, SignInMethod } from '../../model/enums';\n\n/**\n * Provider for generating an {@link PhoneAuthCredential}.\n *\n * @remarks\n * `PhoneAuthProvider` does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * const userCredential = await signInWithCredential(auth, phoneCredential);\n * ```\n *\n * @public\n */\nexport class PhoneAuthProvider {\n  /** Always set to {@link ProviderId}.PHONE. */\n  static readonly PROVIDER_ID: 'phone' = ProviderId.PHONE;\n  /** Always set to {@link SignInMethod}.PHONE. */\n  static readonly PHONE_SIGN_IN_METHOD: 'phone' = SignInMethod.PHONE;\n\n  /** Always set to {@link ProviderId}.PHONE. */\n  readonly providerId = PhoneAuthProvider.PROVIDER_ID;\n  private readonly auth: AuthInternal;\n\n  /**\n   * @param auth - The Firebase {@link Auth} instance in which sign-ins should occur.\n   *\n   */\n  constructor(auth: Auth) {\n    this.auth = _castAuth(auth);\n  }\n\n  /**\n   *\n   * Starts a phone number authentication flow by sending a verification code to the given phone\n   * number.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = await signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param phoneInfoOptions - The user's {@link PhoneInfoOptions}. The phone number should be in\n   * E.164 format (e.g. +***********).\n   * @param applicationVerifier - An {@link ApplicationVerifier}, which prevents\n   * requests from unauthorized clients. This SDK includes an implementation\n   * based on reCAPTCHA v2, {@link RecaptchaVerifier}. If you've enabled\n   * reCAPTCHA Enterprise bot protection in Enforce mode, this parameter is\n   * optional; in all other configurations, the parameter is required.\n   *\n   * @returns A Promise for a verification ID that can be passed to\n   * {@link PhoneAuthProvider.credential} to identify this flow.\n   */\n  verifyPhoneNumber(\n    phoneOptions: PhoneInfoOptions | string,\n    applicationVerifier?: ApplicationVerifier\n  ): Promise<string> {\n    return _verifyPhoneNumber(\n      this.auth,\n      phoneOptions,\n      getModularInstance(applicationVerifier as ApplicationVerifierInternal)\n    );\n  }\n\n  /**\n   * Creates a phone auth credential, given the verification ID from\n   * {@link PhoneAuthProvider.verifyPhoneNumber} and the code that was sent to the user's\n   * mobile device.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = await confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param verificationId - The verification ID returned from {@link PhoneAuthProvider.verifyPhoneNumber}.\n   * @param verificationCode - The verification code sent to the user's mobile device.\n   *\n   * @returns The auth provider credential.\n   */\n  static credential(\n    verificationId: string,\n    verificationCode: string\n  ): PhoneAuthCredential {\n    return PhoneAuthCredential._fromVerification(\n      verificationId,\n      verificationCode\n    );\n  }\n\n  /**\n   * Generates an {@link AuthCredential} from a {@link UserCredential}.\n   * @param userCredential - The user credential.\n   */\n  static credentialFromResult(\n    userCredential: UserCredential\n  ): AuthCredential | null {\n    const credential = userCredential as UserCredentialInternal;\n    return PhoneAuthProvider.credentialFromTaggedObject(credential);\n  }\n\n  /**\n   * Returns an {@link AuthCredential} when passed an error.\n   *\n   * @remarks\n   *\n   * This method works for errors like\n   * `auth/account-exists-with-different-credentials`. This is useful for\n   * recovering when attempting to set a user's phone number but the number\n   * in question is already tied to another account. For example, the following\n   * code tries to update the current user's phone number, and if that\n   * fails, links the user with the account associated with that number:\n   *\n   * ```js\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(number, verifier);\n   * try {\n   *   const code = ''; // Prompt the user for the verification code\n   *   await updatePhoneNumber(\n   *       auth.currentUser,\n   *       PhoneAuthProvider.credential(verificationId, code));\n   * } catch (e) {\n   *   if ((e as FirebaseError)?.code === 'auth/account-exists-with-different-credential') {\n   *     const cred = PhoneAuthProvider.credentialFromError(e);\n   *     await linkWithCredential(auth.currentUser, cred);\n   *   }\n   * }\n   *\n   * // At this point, auth.currentUser.phoneNumber === number.\n   * ```\n   *\n   * @param error - The error to generate a credential from.\n   */\n  static credentialFromError(error: FirebaseError): AuthCredential | null {\n    return PhoneAuthProvider.credentialFromTaggedObject(\n      (error.customData || {}) as TaggedWithTokenResponse\n    );\n  }\n\n  private static credentialFromTaggedObject({\n    _tokenResponse: tokenResponse\n  }: TaggedWithTokenResponse): AuthCredential | null {\n    if (!tokenResponse) {\n      return null;\n    }\n    const { phoneNumber, temporaryProof } =\n      tokenResponse as SignInWithPhoneNumberResponse;\n    if (phoneNumber && temporaryProof) {\n      return PhoneAuthCredential._fromTokenResponse(\n        phoneNumber,\n        temporaryProof\n      );\n    }\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { PopupRedirectResolver } from '../../model/public_types';\nimport { AuthInternal } from '../../model/auth';\nimport { PopupRedirectResolverInternal } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { _getInstance } from './instantiator';\n\n/**\n * Chooses a popup/redirect resolver to use. This prefers the override (which\n * is directly passed in), and falls back to the property set on the auth\n * object. If neither are available, this function errors w/ an argument error.\n */\nexport function _withDefaultResolver(\n  auth: AuthInternal,\n  resolverOverride: PopupRedirectResolver | undefined\n): PopupRedirectResolverInternal {\n  if (resolverOverride) {\n    return _getInstance(resolverOverride);\n  }\n\n  _assert(auth._popupRedirectResolver, auth, AuthErrorCode.ARGUMENT_ERROR);\n\n  return auth._popupRedirectResolver;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  signInWithIdp,\n  SignInWithIdpRequest\n} from '../../api/authentication/idp';\nimport { PhoneOrOauthTokenResponse } from '../../api/authentication/mfa';\nimport { AuthInternal } from '../../model/auth';\nimport { IdTokenResponse } from '../../model/id_token';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthCredential } from '../credentials';\nimport { _link as _linkUser } from '../user/link_unlink';\nimport { _reauthenticate } from '../user/reauthenticate';\nimport { _assert } from '../util/assert';\nimport { _signInWithCredential } from './credential';\nimport { AuthErrorCode } from '../errors';\nimport { ProviderId } from '../../model/enums';\n\nexport interface IdpTaskParams {\n  auth: AuthInternal;\n  requestUri: string;\n  sessionId?: string;\n  tenantId?: string;\n  postBody?: string;\n  pendingToken?: string;\n  user?: UserInternal;\n  bypassAuthState?: boolean;\n}\n\nexport type IdpTask = (\n  params: IdpTaskParams\n) => Promise<UserCredentialInternal>;\n\nclass IdpCredential extends AuthCredential {\n  constructor(readonly params: IdpTaskParams) {\n    super(ProviderId.CUSTOM, ProviderId.CUSTOM);\n  }\n\n  _getIdTokenResponse(auth: AuthInternal): Promise<PhoneOrOauthTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  _linkToIdToken(\n    auth: AuthInternal,\n    idToken: string\n  ): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest(idToken));\n  }\n\n  _getReauthenticationResolver(auth: AuthInternal): Promise<IdTokenResponse> {\n    return signInWithIdp(auth, this._buildIdpRequest());\n  }\n\n  private _buildIdpRequest(idToken?: string): SignInWithIdpRequest {\n    const request: SignInWithIdpRequest = {\n      requestUri: this.params.requestUri,\n      sessionId: this.params.sessionId,\n      postBody: this.params.postBody,\n      tenantId: this.params.tenantId,\n      pendingToken: this.params.pendingToken,\n      returnSecureToken: true,\n      returnIdpCredential: true\n    };\n\n    if (idToken) {\n      request.idToken = idToken;\n    }\n\n    return request;\n  }\n}\n\nexport function _signIn(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  return _signInWithCredential(\n    params.auth,\n    new IdpCredential(params),\n    params.bypassAuthState\n  ) as Promise<UserCredentialInternal>;\n}\n\nexport function _reauth(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _reauthenticate(\n    user,\n    new IdpCredential(params),\n    params.bypassAuthState\n  );\n}\n\nexport async function _link(\n  params: IdpTaskParams\n): Promise<UserCredentialInternal> {\n  const { auth, user } = params;\n  _assert(user, auth, AuthErrorCode.INTERNAL_ERROR);\n  return _linkUser(user, new IdpCredential(params), params.bypassAuthState);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal, UserCredentialInternal } from '../../model/user';\nimport { AuthErrorCode } from '../errors';\nimport { debugAssert, _fail } from '../util/assert';\nimport {\n  _link,\n  _reauth,\n  _signIn,\n  IdpTask,\n  IdpTaskParams\n} from '../strategies/idp';\nimport { AuthInternal } from '../../model/auth';\n\ninterface PendingPromise {\n  resolve: (cred: UserCredentialInternal | null) => void;\n  reject: (error: Error) => void;\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n */\nexport abstract class AbstractPopupRedirectOperation\n  implements AuthEventConsumer\n{\n  private pendingPromise: PendingPromise | null = null;\n  private eventManager: EventManager | null = null;\n  readonly filter: AuthEventType[];\n\n  abstract eventId: string | null;\n\n  constructor(\n    protected readonly auth: AuthInternal,\n    filter: AuthEventType | AuthEventType[],\n    protected readonly resolver: PopupRedirectResolverInternal,\n    protected user?: UserInternal,\n    protected readonly bypassAuthState = false\n  ) {\n    this.filter = Array.isArray(filter) ? filter : [filter];\n  }\n\n  abstract onExecution(): Promise<void>;\n\n  execute(): Promise<UserCredentialInternal | null> {\n    return new Promise<UserCredentialInternal | null>(\n      async (resolve, reject) => {\n        this.pendingPromise = { resolve, reject };\n\n        try {\n          this.eventManager = await this.resolver._initialize(this.auth);\n          await this.onExecution();\n          this.eventManager.registerConsumer(this);\n        } catch (e) {\n          this.reject(e as Error);\n        }\n      }\n    );\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    const { urlResponse, sessionId, postBody, tenantId, error, type } = event;\n    if (error) {\n      this.reject(error);\n      return;\n    }\n\n    const params: IdpTaskParams = {\n      auth: this.auth,\n      requestUri: urlResponse!,\n      sessionId: sessionId!,\n      tenantId: tenantId || undefined,\n      postBody: postBody || undefined,\n      user: this.user,\n      bypassAuthState: this.bypassAuthState\n    };\n\n    try {\n      this.resolve(await this.getIdpTask(type)(params));\n    } catch (e) {\n      this.reject(e as Error);\n    }\n  }\n\n  onError(error: FirebaseError): void {\n    this.reject(error);\n  }\n\n  private getIdpTask(type: AuthEventType): IdpTask {\n    switch (type) {\n      case AuthEventType.SIGN_IN_VIA_POPUP:\n      case AuthEventType.SIGN_IN_VIA_REDIRECT:\n        return _signIn;\n      case AuthEventType.LINK_VIA_POPUP:\n      case AuthEventType.LINK_VIA_REDIRECT:\n        return _link;\n      case AuthEventType.REAUTH_VIA_POPUP:\n      case AuthEventType.REAUTH_VIA_REDIRECT:\n        return _reauth;\n      default:\n        _fail(this.auth, AuthErrorCode.INTERNAL_ERROR);\n    }\n  }\n\n  protected resolve(cred: UserCredentialInternal | null): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.resolve(cred);\n    this.unregisterAndCleanUp();\n  }\n\n  protected reject(error: Error): void {\n    debugAssert(this.pendingPromise, 'Pending promise was never set');\n    this.pendingPromise.reject(error);\n    this.unregisterAndCleanUp();\n  }\n\n  private unregisterAndCleanUp(): void {\n    if (this.eventManager) {\n      this.eventManager.unregisterConsumer(this);\n    }\n\n    this.pendingPromise = null;\n    this.cleanUp();\n  }\n\n  abstract cleanUp(): void;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _assert,\n  debugAssert,\n  _createError,\n  _assertInstanceOf\n} from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport { AuthPopup } from '../util/popup';\nimport { AbstractPopupRedirectOperation } from '../../core/strategies/abstract_popup_redirect_operation';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/*\n * The event timeout is the same on mobile and desktop, no need for Delay. Set this to 8s since\n * blocking functions can take upto 7s to complete sign in, as documented in:\n * https://cloud.google.com/identity-platform/docs/blocking-functions#understanding_blocking_functions\n * https://firebase.google.com/docs/auth/extend-with-blocking-functions#understanding_blocking_functions\n */\nexport const enum _Timeout {\n  AUTH_EVENT = 8000\n}\nexport const _POLL_WINDOW_CLOSE_TIMEOUT = new Delay(2000, 10000);\n\n/**\n * Authenticates a Firebase client using a popup-based OAuth authentication flow.\n *\n * @remarks\n * If succeeds, returns the signed in user along with the provider's credential. If sign in was\n * unsuccessful, returns an error object containing additional information about the error.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n *\n * // The signed-in user info.\n * const user = result.user;\n * // This gives you a Facebook Access Token.\n * const credential = provider.credentialFromResult(auth, result);\n * const token = credential.accessToken;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function signInWithPopup(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _createError(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  const action = new PopupOperation(\n    authInternal,\n    AuthEventType.SIGN_IN_VIA_POPUP,\n    provider,\n    resolverInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a pop-up based\n * OAuth flow.\n *\n * @remarks\n * If the reauthentication is successful, the returned result will contain the user and the\n * provider's credential.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n * // Reauthenticate using a popup.\n * await reauthenticateWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function reauthenticateWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _createError(userInternal.auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.REAUTH_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Links the authenticated provider to the user account using a pop-up based OAuth flow.\n *\n * @remarks\n * If the linking is successful, the returned result will contain the user and the provider's credential.\n *\n * This method does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a popup.\n * const provider = new FacebookAuthProvider();\n * await linkWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function linkWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.LINK_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n *\n */\nclass PopupOperation extends AbstractPopupRedirectOperation {\n  // Only one popup is ever shown at once. The lifecycle of the current popup\n  // can be managed / cancelled by the constructor.\n  private static currentPopupAction: PopupOperation | null = null;\n  private authWindow: AuthPopup | null = null;\n  private pollId: number | null = null;\n\n  constructor(\n    auth: AuthInternal,\n    filter: AuthEventType,\n    private readonly provider: AuthProvider,\n    resolver: PopupRedirectResolverInternal,\n    user?: UserInternal\n  ) {\n    super(auth, filter, resolver, user);\n    if (PopupOperation.currentPopupAction) {\n      PopupOperation.currentPopupAction.cancel();\n    }\n\n    PopupOperation.currentPopupAction = this;\n  }\n\n  async executeNotNull(): Promise<UserCredential> {\n    const result = await this.execute();\n    _assert(result, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return result;\n  }\n\n  async onExecution(): Promise<void> {\n    debugAssert(\n      this.filter.length === 1,\n      'Popup operations only handle one event'\n    );\n    const eventId = _generateEventId();\n    this.authWindow = await this.resolver._openPopup(\n      this.auth,\n      this.provider,\n      this.filter[0], // There's always one, see constructor\n      eventId\n    );\n    this.authWindow.associatedEvent = eventId;\n\n    // Check for web storage support and origin validation _after_ the popup is\n    // loaded. These operations are slow (~1 second or so) Rather than\n    // waiting on them before opening the window, optimistically open the popup\n    // and check for storage support at the same time. If storage support is\n    // not available, this will cause the whole thing to reject properly. It\n    // will also close the popup, but since the promise has already rejected,\n    // the popup closed by user poll will reject into the void.\n    this.resolver._originValidation(this.auth).catch(e => {\n      this.reject(e);\n    });\n\n    this.resolver._isIframeWebStorageSupported(this.auth, isSupported => {\n      if (!isSupported) {\n        this.reject(\n          _createError(this.auth, AuthErrorCode.WEB_STORAGE_UNSUPPORTED)\n        );\n      }\n    });\n\n    // Handle user closure. Notice this does *not* use await\n    this.pollUserCancellation();\n  }\n\n  get eventId(): string | null {\n    return this.authWindow?.associatedEvent || null;\n  }\n\n  cancel(): void {\n    this.reject(_createError(this.auth, AuthErrorCode.EXPIRED_POPUP_REQUEST));\n  }\n\n  cleanUp(): void {\n    if (this.authWindow) {\n      this.authWindow.close();\n    }\n\n    if (this.pollId) {\n      window.clearTimeout(this.pollId);\n    }\n\n    this.authWindow = null;\n    this.pollId = null;\n    PopupOperation.currentPopupAction = null;\n  }\n\n  private pollUserCancellation(): void {\n    const poll = (): void => {\n      if (this.authWindow?.window?.closed) {\n        // Make sure that there is sufficient time for whatever action to\n        // complete. The window could have closed but the sign in network\n        // call could still be in flight. This is specifically true for\n        // Firefox or if the opener is in an iframe, in which case the oauth\n        // helper closes the popup.\n        this.pollId = window.setTimeout(() => {\n          this.pollId = null;\n          this.reject(\n            _createError(this.auth, AuthErrorCode.POPUP_CLOSED_BY_USER)\n          );\n        }, _Timeout.AUTH_EVENT);\n        return;\n      }\n\n      this.pollId = window.setTimeout(poll, _POLL_WINDOW_CLOSE_TIMEOUT.get());\n    };\n\n    poll();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserCredentialInternal } from '../../model/user';\nimport { PersistenceInternal } from '../persistence';\nimport { _persistenceKeyName } from '../persistence/persistence_user_manager';\nimport { _getInstance } from '../util/instantiator';\nimport { AbstractPopupRedirectOperation } from './abstract_popup_redirect_operation';\n\nconst PENDING_REDIRECT_KEY = 'pendingRedirect';\n\n// We only get one redirect outcome for any one auth, so just store it\n// in here.\nconst redirectOutcomeMap: Map<\n  string,\n  () => Promise<UserCredentialInternal | null>\n> = new Map();\n\nexport class RedirectAction extends AbstractPopupRedirectOperation {\n  eventId = null;\n\n  constructor(\n    auth: AuthInternal,\n    resolver: PopupRedirectResolverInternal,\n    bypassAuthState = false\n  ) {\n    super(\n      auth,\n      [\n        AuthEventType.SIGN_IN_VIA_REDIRECT,\n        AuthEventType.LINK_VIA_REDIRECT,\n        AuthEventType.REAUTH_VIA_REDIRECT,\n        AuthEventType.UNKNOWN\n      ],\n      resolver,\n      undefined,\n      bypassAuthState\n    );\n  }\n\n  /**\n   * Override the execute function; if we already have a redirect result, then\n   * just return it.\n   */\n  async execute(): Promise<UserCredentialInternal | null> {\n    let readyOutcome = redirectOutcomeMap.get(this.auth._key());\n    if (!readyOutcome) {\n      try {\n        const hasPendingRedirect = await _getAndClearPendingRedirectStatus(\n          this.resolver,\n          this.auth\n        );\n        const result = hasPendingRedirect ? await super.execute() : null;\n        readyOutcome = () => Promise.resolve(result);\n      } catch (e) {\n        readyOutcome = () => Promise.reject(e);\n      }\n\n      redirectOutcomeMap.set(this.auth._key(), readyOutcome);\n    }\n\n    // If we're not bypassing auth state, the ready outcome should be set to\n    // null.\n    if (!this.bypassAuthState) {\n      redirectOutcomeMap.set(this.auth._key(), () => Promise.resolve(null));\n    }\n\n    return readyOutcome();\n  }\n\n  async onAuthEvent(event: AuthEvent): Promise<void> {\n    if (event.type === AuthEventType.SIGN_IN_VIA_REDIRECT) {\n      return super.onAuthEvent(event);\n    } else if (event.type === AuthEventType.UNKNOWN) {\n      // This is a sentinel value indicating there's no pending redirect\n      this.resolve(null);\n      return;\n    }\n\n    if (event.eventId) {\n      const user = await this.auth._redirectUserForId(event.eventId);\n      if (user) {\n        this.user = user;\n        return super.onAuthEvent(event);\n      } else {\n        this.resolve(null);\n      }\n    }\n  }\n\n  async onExecution(): Promise<void> {}\n\n  cleanUp(): void {}\n}\n\nexport async function _getAndClearPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<boolean> {\n  const key = pendingRedirectKey(auth);\n  const persistence = resolverPersistence(resolver);\n  if (!(await persistence._isAvailable())) {\n    return false;\n  }\n  const hasPendingRedirect = (await persistence._get(key)) === 'true';\n  await persistence._remove(key);\n  return hasPendingRedirect;\n}\n\nexport async function _setPendingRedirectStatus(\n  resolver: PopupRedirectResolverInternal,\n  auth: AuthInternal\n): Promise<void> {\n  return resolverPersistence(resolver)._set(pendingRedirectKey(auth), 'true');\n}\n\nexport function _clearRedirectOutcomes(): void {\n  redirectOutcomeMap.clear();\n}\n\nexport function _overrideRedirectResult(\n  auth: AuthInternal,\n  result: () => Promise<UserCredentialInternal | null>\n): void {\n  redirectOutcomeMap.set(auth._key(), result);\n}\n\nfunction resolverPersistence(\n  resolver: PopupRedirectResolverInternal\n): PersistenceInternal {\n  return _getInstance(resolver._redirectPersistence);\n}\n\nfunction pendingRedirectKey(auth: AuthInternal): string {\n  return _persistenceKeyName(\n    PENDING_REDIRECT_KEY,\n    auth.config.apiKey,\n    auth.name\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { _assertLinkedStatus } from '../../core/user/link_unlink';\nimport {\n  _assertInstanceOf,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport {\n  RedirectAction,\n  _setPendingRedirectStatus\n} from '../../core/strategies/redirect';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/**\n * Authenticates a Firebase client using a full-page redirect flow.\n *\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link signInWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _signInWithRedirect(auth, provider, resolver) as Promise<never>;\n}\n\nexport async function _signInWithRedirect(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await authInternal._initializationPromise;\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  await _setPendingRedirectStatus(resolverInternal, authInternal);\n\n  return resolverInternal._openRedirect(\n    authInternal,\n    provider,\n    AuthEventType.SIGN_IN_VIA_REDIRECT\n  );\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link reauthenticateWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * // Reauthenticate using a redirect.\n * await reauthenticateWithRedirect(result.user, provider);\n * // This will again trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _reauthenticateWithRedirect(\n    user,\n    provider,\n    resolver\n  ) as Promise<never>;\n}\nexport async function _reauthenticateWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.REAUTH_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Links the {@link OAuthProvider} to the user account using a full-page redirect flow.\n * @remarks\n * To handle the results and errors for this operation, refer to {@link getRedirectResult}.\n * Follow the {@link https://firebase.google.com/docs/auth/web/redirect-best-practices\n * | best practices} when using {@link linkWithRedirect}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances\n * created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a redirect.\n * const provider = new FacebookAuthProvider();\n * await linkWithRedirect(result.user, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport function linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<never> {\n  return _linkWithRedirect(user, provider, resolver) as Promise<never>;\n}\nexport async function _linkWithRedirect(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<void | never> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  // Wait for auth initialization to complete, this will process pending redirects and clear the\n  // PENDING_REDIRECT_KEY in persistence. This should be completed before starting a new\n  // redirect and creating a PENDING_REDIRECT_KEY entry.\n  await userInternal.auth._initializationPromise;\n  // Allow the resolver to error before persisting the redirect user\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  await _assertLinkedStatus(false, userInternal, provider.providerId);\n  await _setPendingRedirectStatus(resolverInternal, userInternal.auth);\n\n  const eventId = await prepareUserForRedirect(userInternal);\n  return resolverInternal._openRedirect(\n    userInternal.auth,\n    provider,\n    AuthEventType.LINK_VIA_REDIRECT,\n    eventId\n  );\n}\n\n/**\n * Returns a {@link UserCredential} from the redirect-based sign-in flow.\n *\n * @remarks\n * If sign-in succeeded, returns the signed in user. If sign-in was unsuccessful, fails with an\n * error. If no redirect operation was called, returns `null`.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a redirect.\n * const provider = new FacebookAuthProvider();\n * // You can add additional scopes to the provider:\n * provider.addScope('user_birthday');\n * // Start a sign in process for an unauthenticated user.\n * await signInWithRedirect(auth, provider);\n * // This will trigger a full page redirect away from your app\n *\n * // After returning from the redirect when your app initializes you can obtain the result\n * const result = await getRedirectResult(auth);\n * if (result) {\n *   // This is the signed-in user\n *   const user = result.user;\n *   // This gives you a Facebook Access Token.\n *   const credential = provider.credentialFromResult(auth, result);\n *   const token = credential.accessToken;\n * }\n * // As this API can be used for sign-in, linking and reauthentication,\n * // check the operationType to determine what triggered this redirect\n * // operation.\n * const operationType = result.operationType;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function getRedirectResult(\n  auth: Auth,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential | null> {\n  await _castAuth(auth)._initializationPromise;\n  return _getRedirectResult(auth, resolver, false);\n}\n\nexport async function _getRedirectResult(\n  auth: Auth,\n  resolverExtern?: PopupRedirectResolver,\n  bypassAuthState = false\n): Promise<UserCredential | null> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const resolver = _withDefaultResolver(authInternal, resolverExtern);\n  const action = new RedirectAction(authInternal, resolver, bypassAuthState);\n  const result = await action.execute();\n\n  if (result && !bypassAuthState) {\n    delete result.user._redirectEventId;\n    await authInternal._persistUserIfCurrent(result.user as UserInternal);\n    await authInternal._setRedirectUser(null, resolverExtern);\n  }\n\n  return result;\n}\n\nasync function prepareUserForRedirect(user: UserInternal): Promise<string> {\n  const eventId = _generateEventId(`${user.uid}:::`);\n  user._redirectEventId = eventId;\n  await user.auth._setRedirectUser(user);\n  await user.auth._persistUserIfCurrent(user);\n  return eventId;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AuthEvent,\n  AuthEventConsumer,\n  AuthEventType,\n  EventManager\n} from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { AuthInternal } from '../../model/auth';\nimport { _createError } from '../util/assert';\n\n// The amount of time to store the UIDs of seen events; this is\n// set to 10 min by default\nconst EVENT_DUPLICATION_CACHE_DURATION_MS = 10 * 60 * 1000;\n\nexport class AuthEventManager implements EventManager {\n  private readonly cachedEventUids: Set<string> = new Set();\n  private readonly consumers: Set<AuthEventConsumer> = new Set();\n  protected queuedRedirectEvent: AuthEvent | null = null;\n  protected hasHandledPotentialRedirect = false;\n  private lastProcessedEventTime = Date.now();\n\n  constructor(private readonly auth: AuthInternal) {}\n\n  registerConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.add(authEventConsumer);\n\n    if (\n      this.queuedRedirectEvent &&\n      this.isEventForConsumer(this.queuedRedirectEvent, authEventConsumer)\n    ) {\n      this.sendToConsumer(this.queuedRedirectEvent, authEventConsumer);\n      this.saveEventToCache(this.queuedRedirectEvent);\n      this.queuedRedirectEvent = null;\n    }\n  }\n\n  unregisterConsumer(authEventConsumer: AuthEventConsumer): void {\n    this.consumers.delete(authEventConsumer);\n  }\n\n  onEvent(event: AuthEvent): boolean {\n    // Check if the event has already been handled\n    if (this.hasEventBeenHandled(event)) {\n      return false;\n    }\n\n    let handled = false;\n    this.consumers.forEach(consumer => {\n      if (this.isEventForConsumer(event, consumer)) {\n        handled = true;\n        this.sendToConsumer(event, consumer);\n        this.saveEventToCache(event);\n      }\n    });\n\n    if (this.hasHandledPotentialRedirect || !isRedirectEvent(event)) {\n      // If we've already seen a redirect before, or this is a popup event,\n      // bail now\n      return handled;\n    }\n\n    this.hasHandledPotentialRedirect = true;\n\n    // If the redirect wasn't handled, hang on to it\n    if (!handled) {\n      this.queuedRedirectEvent = event;\n      handled = true;\n    }\n\n    return handled;\n  }\n\n  private sendToConsumer(event: AuthEvent, consumer: AuthEventConsumer): void {\n    if (event.error && !isNullRedirectEvent(event)) {\n      const code =\n        (event.error.code?.split('auth/')[1] as AuthErrorCode) ||\n        AuthErrorCode.INTERNAL_ERROR;\n      consumer.onError(_createError(this.auth, code));\n    } else {\n      consumer.onAuthEvent(event);\n    }\n  }\n\n  private isEventForConsumer(\n    event: AuthEvent,\n    consumer: AuthEventConsumer\n  ): boolean {\n    const eventIdMatches =\n      consumer.eventId === null ||\n      (!!event.eventId && event.eventId === consumer.eventId);\n    return consumer.filter.includes(event.type) && eventIdMatches;\n  }\n\n  private hasEventBeenHandled(event: AuthEvent): boolean {\n    if (\n      Date.now() - this.lastProcessedEventTime >=\n      EVENT_DUPLICATION_CACHE_DURATION_MS\n    ) {\n      this.cachedEventUids.clear();\n    }\n\n    return this.cachedEventUids.has(eventUid(event));\n  }\n\n  private saveEventToCache(event: AuthEvent): void {\n    this.cachedEventUids.add(eventUid(event));\n    this.lastProcessedEventTime = Date.now();\n  }\n}\n\nfunction eventUid(e: AuthEvent): string {\n  return [e.type, e.eventId, e.sessionId, e.tenantId].filter(v => v).join('-');\n}\n\nfunction isNullRedirectEvent({ type, error }: AuthEvent): boolean {\n  return (\n    type === AuthEventType.UNKNOWN &&\n    error?.code === `auth/${AuthErrorCode.NO_AUTH_EVENT}`\n  );\n}\n\nfunction isRedirectEvent(event: AuthEvent): boolean {\n  switch (event.type) {\n    case AuthEventType.SIGN_IN_VIA_REDIRECT:\n    case AuthEventType.LINK_VIA_REDIRECT:\n    case AuthEventType.REAUTH_VIA_REDIRECT:\n      return true;\n    case AuthEventType.UNKNOWN:\n      return isNullRedirectEvent(event);\n    default:\n      return false;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _performApiRequest, Endpoint, HttpMethod } from '../index';\nimport { Auth } from '../../model/public_types';\n\nexport interface GetProjectConfigRequest {\n  androidPackageName?: string;\n  iosBundleId?: string;\n}\n\nexport interface GetProjectConfigResponse {\n  authorizedDomains: string[];\n}\n\nexport async function _getProjectConfig(\n  auth: Auth,\n  request: GetProjectConfigRequest = {}\n): Promise<GetProjectConfigResponse> {\n  return _performApiRequest<GetProjectConfigRequest, GetProjectConfigResponse>(\n    auth,\n    HttpMethod.GET,\n    Endpoint.GET_PROJECT_CONFIG,\n    request\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProjectConfig } from '../../api/project_config/get_project_config';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthErrorCode } from '../errors';\nimport { _fail } from './assert';\nimport { _getCurrentUrl } from './location';\n\nconst IP_ADDRESS_REGEX = /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/;\nconst HTTP_REGEX = /^https?/;\n\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  // Skip origin validation if we are in an emulated environment\n  if (auth.config.emulator) {\n    return;\n  }\n\n  const { authorizedDomains } = await _getProjectConfig(auth);\n\n  for (const domain of authorizedDomains) {\n    try {\n      if (matchDomain(domain)) {\n        return;\n      }\n    } catch {\n      // Do nothing if there's a URL error; just continue searching\n    }\n  }\n\n  // In the old SDK, this error also provides helpful messages.\n  _fail(auth, AuthErrorCode.INVALID_ORIGIN);\n}\n\nfunction matchDomain(expected: string): boolean {\n  const currentUrl = _getCurrentUrl();\n  const { protocol, hostname } = new URL(currentUrl);\n  if (expected.startsWith('chrome-extension://')) {\n    const ceUrl = new URL(expected);\n\n    if (ceUrl.hostname === '' && hostname === '') {\n      // For some reason we're not parsing chrome URLs properly\n      return (\n        protocol === 'chrome-extension:' &&\n        expected.replace('chrome-extension://', '') ===\n          currentUrl.replace('chrome-extension://', '')\n      );\n    }\n\n    return protocol === 'chrome-extension:' && ceUrl.hostname === hostname;\n  }\n\n  if (!HTTP_REGEX.test(protocol)) {\n    return false;\n  }\n\n  if (IP_ADDRESS_REGEX.test(expected)) {\n    // The domain has to be exactly equal to the pattern, as an IP domain will\n    // only contain the IP, no extra character.\n    return hostname === expected;\n  }\n\n  // Dots in pattern should be escaped.\n  const escapedDomainPattern = expected.replace(/\\./g, '\\\\.');\n  // Non ip address domains.\n  // domain.com = *.domain.com OR domain.com\n  const re = new RegExp(\n    '^(.+\\\\.' + escapedDomainPattern + '|' + escapedDomainPattern + ')$',\n    'i'\n  );\n  return re.test(hostname);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as js from '../load_js';\n\nconst NETWORK_TIMEOUT = new Delay(30000, 60000);\n\n/**\n * Reset unloaded GApi modules. If gapi.load fails due to a network error,\n * it will stop working after a retrial. This is a hack to fix this issue.\n */\nfunction resetUnloadedGapiModules(): void {\n  // Clear last failed gapi.load state to force next gapi.load to first\n  // load the failed gapi.iframes module.\n  // Get gapix.beacon context.\n  const beacon = _window().___jsl;\n  // Get current hint.\n  if (beacon?.H) {\n    // Get gapi hint.\n    for (const hint of Object.keys(beacon.H)) {\n      // Requested modules.\n      beacon.H[hint].r = beacon.H[hint].r || [];\n      // Loaded modules.\n      beacon.H[hint].L = beacon.H[hint].L || [];\n      // Set requested modules to a copy of the loaded modules.\n      beacon.H[hint].r = [...beacon.H[hint].L];\n      // Clear pending callbacks.\n      if (beacon.CP) {\n        for (let i = 0; i < beacon.CP.length; i++) {\n          // Remove all failed pending callbacks.\n          beacon.CP[i] = null;\n        }\n      }\n    }\n  }\n}\n\nfunction loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  return new Promise<gapi.iframes.Context>((resolve, reject) => {\n    // Function to run when gapi.load is ready.\n    function loadGapiIframe(): void {\n      // The developer may have tried to previously run gapi.load and failed.\n      // Run this to fix that.\n      resetUnloadedGapiModules();\n      gapi.load('gapi.iframes', {\n        callback: () => {\n          resolve(gapi.iframes.getContext());\n        },\n        ontimeout: () => {\n          // The above reset may be sufficient, but having this reset after\n          // failure ensures that if the developer calls gapi.load after the\n          // connection is re-established and before another attempt to embed\n          // the iframe, it would work and would not be broken because of our\n          // failed attempt.\n          // Timeout when gapi.iframes.Iframe not loaded.\n          resetUnloadedGapiModules();\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        },\n        timeout: NETWORK_TIMEOUT.get()\n      });\n    }\n\n    if (_window().gapi?.iframes?.Iframe) {\n      // If gapi.iframes.Iframe available, resolve.\n      resolve(gapi.iframes.getContext());\n    } else if (!!_window().gapi?.load) {\n      // Gapi loader ready, load gapi.iframes.\n      loadGapiIframe();\n    } else {\n      // Create a new iframe callback when this is called so as not to overwrite\n      // any previous defined callback. This happens if this method is called\n      // multiple times in parallel and could result in the later callback\n      // overwriting the previous one. This would end up with a iframe\n      // timeout.\n      const cbName = js._generateCallbackName('iframefcb');\n      // GApi loader not available, dynamically load platform.js.\n      _window()[cbName] = () => {\n        // GApi loader should be ready.\n        if (!!gapi.load) {\n          loadGapiIframe();\n        } else {\n          // Gapi loader failed, throw error.\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        }\n      };\n      // Load GApi loader.\n      return js\n        ._loadJS(`${js._gapiScriptUrl()}?onload=${cbName}`)\n        .catch(e => reject(e));\n    }\n  }).catch(error => {\n    // Reset cached promise to allow for retrial.\n    cachedGApiLoader = null;\n    throw error;\n  });\n}\n\nlet cachedGApiLoader: Promise<gapi.iframes.Context> | null = null;\nexport function _loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  cachedGApiLoader = cachedGApiLoader || loadGapi(auth);\n  return cachedGApiLoader;\n}\n\nexport function _resetLoader(): void {\n  cachedGApiLoader = null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { querystring } from '@firebase/util';\nimport { DefaultConfig } from '../../../internal';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _emulatorUrl } from '../../core/util/emulator';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as gapiLoader from './gapi';\n\nconst PING_TIMEOUT = new Delay(5000, 15000);\nconst IFRAME_PATH = '__/auth/iframe';\nconst EMULATED_IFRAME_PATH = 'emulator/auth/iframe';\n\nconst IFRAME_ATTRIBUTES = {\n  style: {\n    position: 'absolute',\n    top: '-100px',\n    width: '1px',\n    height: '1px'\n  },\n  'aria-hidden': 'true',\n  tabindex: '-1'\n};\n\n// Map from apiHost to endpoint ID for passing into iframe. In current SDK, apiHost can be set to\n// anything (not from a list of endpoints with IDs as in legacy), so this is the closest we can get.\nconst EID_FROM_APIHOST = new Map([\n  [DefaultConfig.API_HOST, 'p'], // production\n  ['staging-identitytoolkit.sandbox.googleapis.com', 's'], // staging\n  ['test-identitytoolkit.sandbox.googleapis.com', 't'] // test\n]);\n\nfunction getIframeUrl(auth: AuthInternal): string {\n  const config = auth.config;\n  _assert(config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  const url = config.emulator\n    ? _emulatorUrl(config, EMULATED_IFRAME_PATH)\n    : `https://${auth.config.authDomain}/${IFRAME_PATH}`;\n\n  const params: Record<string, string> = {\n    apiKey: config.apiKey,\n    appName: auth.name,\n    v: SDK_VERSION\n  };\n  const eid = EID_FROM_APIHOST.get(auth.config.apiHost);\n  if (eid) {\n    params.eid = eid;\n  }\n  const frameworks = auth._getFrameworks();\n  if (frameworks.length) {\n    params.fw = frameworks.join(',');\n  }\n  return `${url}?${querystring(params).slice(1)}`;\n}\n\nexport async function _openIframe(\n  auth: AuthInternal\n): Promise<gapi.iframes.Iframe> {\n  const context = await gapiLoader._loadGapi(auth);\n  const gapi = _window().gapi;\n  _assert(gapi, auth, AuthErrorCode.INTERNAL_ERROR);\n  return context.open(\n    {\n      where: document.body,\n      url: getIframeUrl(auth),\n      messageHandlersFilter: gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,\n      attributes: IFRAME_ATTRIBUTES,\n      dontclear: true\n    },\n    (iframe: gapi.iframes.Iframe) =>\n      new Promise(async (resolve, reject) => {\n        await iframe.restyle({\n          // Prevent iframe from closing on mouse out.\n          setHideOnLeave: false\n        });\n\n        const networkError = _createError(\n          auth,\n          AuthErrorCode.NETWORK_REQUEST_FAILED\n        );\n        // Confirm iframe is correctly loaded.\n        // To fallback on failure, set a timeout.\n        const networkErrorTimer = _window().setTimeout(() => {\n          reject(networkError);\n        }, PING_TIMEOUT.get());\n        // Clear timer and resolve pending iframe ready promise.\n        function clearTimerAndResolve(): void {\n          _window().clearTimeout(networkErrorTimer);\n          resolve(iframe);\n        }\n        // This returns an IThenable. However the reject part does not call\n        // when the iframe is not loaded.\n        iframe.ping(clearTimerAndResolve).then(clearTimerAndResolve, () => {\n          reject(networkError);\n        });\n      })\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getUA } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport {\n  _isChromeIOS,\n  _isFirefox,\n  _isIOSStandalone\n} from '../../core/util/browser';\nimport { AuthInternal } from '../../model/auth';\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\n\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\n\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nexport class AuthPopup {\n  associatedEvent: string | null = null;\n\n  constructor(readonly window: Window | null) {}\n\n  close(): void {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n}\n\nexport function _open(\n  auth: AuthInternal,\n  url?: string,\n  name?: string,\n  width = DEFAULT_WIDTH,\n  height = DEFAULT_HEIGHT\n): AuthPopup {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n\n  const options: { [key: string]: string } = {\n    ...BASE_POPUP_OPTIONS,\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  };\n\n  // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL;\n    // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce(\n    (accum, [key, value]) => `${accum}${key}=${value},`,\n    ''\n  );\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  }\n\n  // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n  const newWin = window.open(url || '', target, optionsString);\n  _assert(newWin, auth, AuthErrorCode.POPUP_BLOCKED);\n\n  // Flaky on IE edge, encapsulate with a try and catch.\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url: string, target: string): void {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent(\n    'click',\n    true,\n    true,\n    window,\n    1,\n    0,\n    0,\n    0,\n    0,\n    false,\n    false,\n    false,\n    false,\n    1,\n    null\n  );\n  el.dispatchEvent(click);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { AuthProvider } from '../../model/public_types';\nimport { ApiKey, AppName, AuthInternal } from '../../model/auth';\nimport { AuthEventType } from '../../model/popup_redirect';\nimport { AuthErrorCode } from '../errors';\nimport { _assert } from './assert';\nimport { isEmpty, querystring } from '@firebase/util';\nimport { _emulatorUrl } from './emulator';\nimport { FederatedAuthProvider } from '../providers/federated';\nimport { BaseOAuthProvider } from '../providers/oauth';\n\n/**\n * URL for Authentication widget which will initiate the OAuth handshake\n *\n * @internal\n */\nconst WIDGET_PATH = '__/auth/handler';\n\n/**\n * URL for emulated environment\n *\n * @internal\n */\nconst EMULATOR_WIDGET_PATH = 'emulator/auth/handler';\n\n/**\n * Fragment name for the App Check token that gets passed to the widget\n *\n * @internal\n */\nconst FIREBASE_APP_CHECK_FRAGMENT_ID = encodeURIComponent('fac');\n\n// eslint-disable-next-line @typescript-eslint/consistent-type-definitions\ntype WidgetParams = {\n  apiKey: ApiKey;\n  appName: AppName;\n  authType: AuthEventType;\n  redirectUrl?: string;\n  v: string;\n  providerId?: string;\n  scopes?: string;\n  customParameters?: string;\n  eventId?: string;\n  tid?: string;\n} & { [key: string]: string | undefined };\n\nexport async function _getRedirectUrl(\n  auth: AuthInternal,\n  provider: AuthProvider,\n  authType: AuthEventType,\n  redirectUrl?: string,\n  eventId?: string,\n  additionalParams?: Record<string, string>\n): Promise<string> {\n  _assert(auth.config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  _assert(auth.config.apiKey, auth, AuthErrorCode.INVALID_API_KEY);\n\n  const params: WidgetParams = {\n    apiKey: auth.config.apiKey,\n    appName: auth.name,\n    authType,\n    redirectUrl,\n    v: SDK_VERSION,\n    eventId\n  };\n\n  if (provider instanceof FederatedAuthProvider) {\n    provider.setDefaultLanguage(auth.languageCode);\n    params.providerId = provider.providerId || '';\n    if (!isEmpty(provider.getCustomParameters())) {\n      params.customParameters = JSON.stringify(provider.getCustomParameters());\n    }\n\n    // TODO set additionalParams from the provider as well?\n    for (const [key, value] of Object.entries(additionalParams || {})) {\n      params[key] = value;\n    }\n  }\n\n  if (provider instanceof BaseOAuthProvider) {\n    const scopes = provider.getScopes().filter(scope => scope !== '');\n    if (scopes.length > 0) {\n      params.scopes = scopes.join(',');\n    }\n  }\n\n  if (auth.tenantId) {\n    params.tid = auth.tenantId;\n  }\n\n  // TODO: maybe set eid as endpointId\n  // TODO: maybe set fw as Frameworks.join(\",\")\n\n  const paramsDict = params as Record<string, string | number>;\n  for (const key of Object.keys(paramsDict)) {\n    if (paramsDict[key] === undefined) {\n      delete paramsDict[key];\n    }\n  }\n\n  // Sets the App Check token to pass to the widget\n  const appCheckToken = await auth._getAppCheckToken();\n  const appCheckTokenFragment = appCheckToken\n    ? `#${FIREBASE_APP_CHECK_FRAGMENT_ID}=${encodeURIComponent(appCheckToken)}`\n    : '';\n\n  // Start at index 1 to skip the leading '&' in the query string\n  return `${getHandlerBase(auth)}?${querystring(paramsDict).slice(\n    1\n  )}${appCheckTokenFragment}`;\n}\n\nfunction getHandlerBase({ config }: AuthInternal): string {\n  if (!config.emulator) {\n    return `https://${config.authDomain}/${WIDGET_PATH}`;\n  }\n\n  return _emulatorUrl(config, EMULATOR_WIDGET_PATH);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../model/public_types';\n\nimport { AuthEventManager } from '../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../core/errors';\nimport { _assert, debugAssert, _fail } from '../core/util/assert';\nimport { _generateEventId } from '../core/util/event_id';\nimport { _getCurrentUrl } from '../core/util/location';\nimport { _validateOrigin } from '../core/util/validate_origin';\nimport { AuthInternal } from '../model/auth';\nimport {\n  AuthEventType,\n  EventManager,\n  GapiAuthEvent,\n  GapiOutcome,\n  PopupRedirectResolverInternal\n} from '../model/popup_redirect';\nimport { _setWindowLocation } from './auth_window';\nimport { _openIframe } from './iframe/iframe';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { _open, AuthPopup } from './util/popup';\nimport { _getRedirectResult } from './strategies/redirect';\nimport { _getRedirectUrl } from '../core/util/handler';\nimport { _isIOS, _isMobileBrowser, _isSafari } from '../core/util/browser';\nimport { _overrideRedirectResult } from '../core/strategies/redirect';\n\n/**\n * The special web storage event\n *\n */\nconst WEB_STORAGE_SUPPORT_KEY = 'webStorageSupport';\n\ninterface WebStorageSupportMessage extends gapi.iframes.Message {\n  [index: number]: Record<string, boolean>;\n}\n\ninterface ManagerOrPromise {\n  manager?: EventManager;\n  promise?: Promise<EventManager>;\n}\n\nclass BrowserPopupRedirectResolver implements PopupRedirectResolverInternal {\n  private readonly eventManagers: Record<string, ManagerOrPromise> = {};\n  private readonly iframes: Record<string, gapi.iframes.Iframe> = {};\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  readonly _redirectPersistence = browserSessionPersistence;\n\n  // Wrapping in async even though we don't await anywhere in order\n  // to make sure errors are raised as promise rejections\n  async _openPopup(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<AuthPopup> {\n    debugAssert(\n      this.eventManagers[auth._key()]?.manager,\n      '_initialize() not called before _openPopup()'\n    );\n\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    return _open(auth, url, _generateEventId());\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<never> {\n    await this._originValidation(auth);\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    _setWindowLocation(url);\n    return new Promise(() => {});\n  }\n\n  _initialize(auth: AuthInternal): Promise<EventManager> {\n    const key = auth._key();\n    if (this.eventManagers[key]) {\n      const { manager, promise } = this.eventManagers[key];\n      if (manager) {\n        return Promise.resolve(manager);\n      } else {\n        debugAssert(promise, 'If manager is not set, promise should be');\n        return promise;\n      }\n    }\n\n    const promise = this.initAndGetManager(auth);\n    this.eventManagers[key] = { promise };\n\n    // If the promise is rejected, the key should be removed so that the\n    // operation can be retried later.\n    promise.catch(() => {\n      delete this.eventManagers[key];\n    });\n\n    return promise;\n  }\n\n  private async initAndGetManager(auth: AuthInternal): Promise<EventManager> {\n    const iframe = await _openIframe(auth);\n    const manager = new AuthEventManager(auth);\n    iframe.register<GapiAuthEvent>(\n      'authEvent',\n      (iframeEvent: GapiAuthEvent | null) => {\n        _assert(iframeEvent?.authEvent, auth, AuthErrorCode.INVALID_AUTH_EVENT);\n        // TODO: Consider splitting redirect and popup events earlier on\n\n        const handled = manager.onEvent(iframeEvent.authEvent);\n        return { status: handled ? GapiOutcome.ACK : GapiOutcome.ERROR };\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n\n    this.eventManagers[auth._key()] = { manager };\n    this.iframes[auth._key()] = iframe;\n    return manager;\n  }\n\n  _isIframeWebStorageSupported(\n    auth: AuthInternal,\n    cb: (supported: boolean) => unknown\n  ): void {\n    const iframe = this.iframes[auth._key()];\n    iframe.send<gapi.iframes.Message, WebStorageSupportMessage>(\n      WEB_STORAGE_SUPPORT_KEY,\n      { type: WEB_STORAGE_SUPPORT_KEY },\n      result => {\n        const isSupported = result?.[0]?.[WEB_STORAGE_SUPPORT_KEY];\n        if (isSupported !== undefined) {\n          cb(!!isSupported);\n        }\n\n        _fail(auth, AuthErrorCode.INTERNAL_ERROR);\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  get _shouldInitProactively(): boolean {\n    // Mobile browsers and Safari need to optimistically initialize\n    return _isMobileBrowser() || _isSafari() || _isIOS();\n  }\n\n  _completeRedirectFn = _getRedirectResult;\n\n  _overrideRedirectResult = _overrideRedirectResult;\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for browser\n * based applications.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @public\n */\nexport const browserPopupRedirectResolver: PopupRedirectResolver =\n  BrowserPopupRedirectResolver;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  FactorId,\n  PhoneMultiFactorAssertion\n} from '../../../model/public_types';\n\nimport { MultiFactorAssertionImpl } from '../../../mfa/mfa_assertion';\nimport { AuthInternal } from '../../../model/auth';\nimport { finalizeEnrollPhoneMfa } from '../../../api/account_management/mfa';\nimport { PhoneAuthCredential } from '../../../core/credentials/phone';\nimport {\n  finalizeSignInPhoneMfa,\n  FinalizeMfaResponse\n} from '../../../api/authentication/mfa';\n\n/**\n * {@inheritdoc PhoneMultiFactorAssertion}\n *\n * @public\n */\nexport class PhoneMultiFactorAssertionImpl\n  extends MultiFactorAssertionImpl\n  implements PhoneMultiFactorAssertion\n{\n  private constructor(private readonly credential: PhoneAuthCredential) {\n    super(FactorId.PHONE);\n  }\n\n  /** @internal */\n  static _fromCredential(\n    credential: PhoneAuthCredential\n  ): PhoneMultiFactorAssertionImpl {\n    return new PhoneMultiFactorAssertionImpl(credential);\n  }\n\n  /** @internal */\n  _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeEnrollPhoneMfa(auth, {\n      idToken,\n      displayName,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n\n  /** @internal */\n  _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeSignInPhoneMfa(auth, {\n      mfaPendingCredential,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n}\n\n/**\n * Provider for generating a {@link PhoneMultiFactorAssertion}.\n *\n * @public\n */\nexport class PhoneMultiFactorGenerator {\n  private constructor() {}\n\n  /**\n   * Provides a {@link PhoneMultiFactorAssertion} to confirm ownership of the phone second factor.\n   *\n   * @remarks\n   * This method does not work in a Node.js environment.\n   *\n   * @param phoneAuthCredential - A credential provided by {@link PhoneAuthProvider.credential}.\n   * @returns A {@link PhoneMultiFactorAssertion} which can be used with\n   * {@link MultiFactorResolver.resolveSignIn}\n   */\n  static assertion(credential: PhoneAuthCredential): PhoneMultiFactorAssertion {\n    return PhoneMultiFactorAssertionImpl._fromCredential(credential);\n  }\n\n  /**\n   * The identifier of the phone second factor: `phone`.\n   */\n  static FACTOR_ID = 'phone';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\n\nimport {\n  initializeAuth,\n  beforeAuthStateChanged,\n  onIdTokenChanged,\n  connectAuthEmulator\n} from '..';\nimport { registerAuth } from '../core/auth/register';\nimport { ClientPlatform } from '../core/util/version';\nimport { browserLocalPersistence } from './persistence/local_storage';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { indexedDBLocalPersistence } from './persistence/indexed_db';\nimport { browserPopupRedirectResolver } from './popup_redirect';\nimport { Auth, User } from '../model/public_types';\nimport { getDefaultEmulatorHost, getExperimentalSetting } from '@firebase/util';\nimport { _setExternalJSProvider } from './load_js';\nimport { _createError } from '../core/util/assert';\nimport { AuthErrorCode } from '../core/errors';\n\nconst DEFAULT_ID_TOKEN_MAX_AGE = 5 * 60;\nconst authIdTokenMaxAge =\n  getExperimentalSetting('authIdTokenMaxAge') || DEFAULT_ID_TOKEN_MAX_AGE;\n\nlet lastPostedIdToken: string | undefined | null = null;\n\nconst mintCookieFactory = (url: string) => async (user: User | null) => {\n  const idTokenResult = user && (await user.getIdTokenResult());\n  const idTokenAge =\n    idTokenResult &&\n    (new Date().getTime() - Date.parse(idTokenResult.issuedAtTime)) / 1_000;\n  if (idTokenAge && idTokenAge > authIdTokenMaxAge) {\n    return;\n  }\n  // Specifically trip null => undefined when logged out, to delete any existing cookie\n  const idToken = idTokenResult?.token;\n  if (lastPostedIdToken === idToken) {\n    return;\n  }\n  lastPostedIdToken = idToken;\n  await fetch(url, {\n    method: idToken ? 'POST' : 'DELETE',\n    headers: idToken\n      ? {\n          'Authorization': `Bearer ${idToken}`\n        }\n      : {}\n  });\n};\n\n/**\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\n *\n * @param app - The Firebase App.\n *\n * @public\n */\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    popupRedirectResolver: browserPopupRedirectResolver,\n    persistence: [\n      indexedDBLocalPersistence,\n      browserLocalPersistence,\n      browserSessionPersistence\n    ]\n  });\n\n  const authTokenSyncPath = getExperimentalSetting('authTokenSyncURL');\n  // Only do the Cookie exchange in a secure context\n  if (\n    authTokenSyncPath &&\n    typeof isSecureContext === 'boolean' &&\n    isSecureContext\n  ) {\n    // Don't allow urls (XSS possibility), only paths on the same domain\n    const authTokenSyncUrl = new URL(authTokenSyncPath, location.origin);\n    if (location.origin === authTokenSyncUrl.origin) {\n      const mintCookie = mintCookieFactory(authTokenSyncUrl.toString());\n      beforeAuthStateChanged(auth, mintCookie, () =>\n        mintCookie(auth.currentUser)\n      );\n      onIdTokenChanged(auth, user => mintCookie(user));\n    }\n  }\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nfunction getScriptParentElement(): HTMLDocument | HTMLHeadElement {\n  return document.getElementsByTagName('head')?.[0] ?? document;\n}\n\n_setExternalJSProvider({\n  loadJS(url: string): Promise<Event> {\n    // TODO: consider adding timeout support & cancellation\n    return new Promise((resolve, reject) => {\n      const el = document.createElement('script');\n      el.setAttribute('src', url);\n      el.onload = resolve;\n      el.onerror = e => {\n        const error = _createError(AuthErrorCode.INTERNAL_ERROR);\n        error.customData = e as unknown as Record<string, unknown>;\n        reject(error);\n      };\n      el.type = 'text/javascript';\n      el.charset = 'UTF-8';\n      getScriptParentElement().appendChild(el);\n    });\n  },\n\n  gapiScript: 'https://apis.google.com/js/api.js',\n  recaptchaV2Script: 'https://www.google.com/recaptcha/api.js',\n  recaptchaEnterpriseScript:\n    'https://www.google.com/recaptcha/enterprise.js?render='\n});\n\nregisterAuth(ClientPlatform.BROWSER);\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface CordovaWindow extends Window {\n  cordova: {\n    plugins: {\n      browsertab: {\n        isAvailable(cb: (available: boolean) => void): void;\n        openUrl(url: string): void;\n        close(): void;\n      };\n    };\n\n    InAppBrowser: {\n      open(url: string, target: string, options: string): InAppBrowserRef;\n    };\n  };\n\n  universalLinks: {\n    subscribe(\n      n: null,\n      cb: (event: Record<string, string> | null) => void\n    ): void;\n  };\n\n  BuildInfo: {\n    readonly packageName: string;\n    readonly displayName: string;\n  };\n\n  handleOpenURL(url: string): void;\n}\n\nexport interface InAppBrowserRef {\n  close?: () => void;\n}\n\nexport function _cordovaWindow(): CordovaWindow {\n  return window as unknown as CordovaWindow;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider } from '../../model/public_types';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  debugAssert,\n  _assert,\n  _createError,\n  _fail\n} from '../../core/util/assert';\nimport { _isAndroid, _isIOS, _isIOS7Or8 } from '../../core/util/browser';\nimport { _getRedirectUrl } from '../../core/util/handler';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent } from '../../model/popup_redirect';\nimport { InAppBrowserRef, _cordovaWindow } from '../plugins';\nimport {\n  GetProjectConfigRequest,\n  _getProjectConfig\n} from '../../api/project_config/get_project_config';\n\n/**\n * How long to wait after the app comes back into focus before concluding that\n * the user closed the sign in tab.\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n\n/**\n * Generates the URL for the OAuth handler.\n */\nexport async function _generateHandlerUrl(\n  auth: AuthInternal,\n  event: AuthEvent,\n  provider: AuthProvider\n): Promise<string> {\n  // Get the cordova plugins\n  const { BuildInfo } = _cordovaWindow();\n  debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n  const sessionDigest = await computeSha256(event.sessionId);\n\n  const additionalParams: Record<string, string> = {};\n  if (_isIOS()) {\n    // iOS app identifier\n    additionalParams['ibi'] = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    // Android app identifier\n    additionalParams['apn'] = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Add the display name if available\n  if (BuildInfo.displayName) {\n    additionalParams['appDisplayName'] = BuildInfo.displayName;\n  }\n\n  // Attached the hashed session ID\n  additionalParams['sessionId'] = sessionDigest;\n  return _getRedirectUrl(\n    auth,\n    provider,\n    event.type,\n    undefined,\n    event.eventId ?? undefined,\n    additionalParams\n  );\n}\n\n/**\n * Validates that this app is valid for this project configuration\n */\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  const { BuildInfo } = _cordovaWindow();\n  const request: GetProjectConfigRequest = {};\n  if (_isIOS()) {\n    request.iosBundleId = BuildInfo.packageName;\n  } else if (_isAndroid()) {\n    request.androidPackageName = BuildInfo.packageName;\n  } else {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  // Will fail automatically if package name is not authorized\n  await _getProjectConfig(auth, request);\n}\n\nexport function _performRedirect(\n  handlerUrl: string\n): Promise<InAppBrowserRef | null> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef: InAppBrowserRef | null = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(\n          handlerUrl,\n          _isIOS7Or8() ? '_blank' : '_system',\n          'location=yes'\n        );\n      }\n      resolve(iabRef);\n    });\n  });\n}\n\n// Thin interface wrapper to avoid circular dependency with ./events module\ninterface PassiveAuthEventListener {\n  addPassiveListener(cb: () => void): void;\n  removePassiveListener(cb: () => void): void;\n}\n\n/**\n * This function waits for app activity to be seen before resolving. It does\n * this by attaching listeners to various dom events. Once the app is determined\n * to be visible, this promise resolves. AFTER that resolution, the listeners\n * are detached and any browser tabs left open will be closed.\n */\nexport async function _waitForAppResume(\n  auth: AuthInternal,\n  eventListener: PassiveAuthEventListener,\n  iabRef: InAppBrowserRef | null\n): Promise<void> {\n  // Get the cordova plugins\n  const { cordova } = _cordovaWindow();\n\n  let cleanup = (): void => {};\n  try {\n    await new Promise<void>((resolve, reject) => {\n      let onCloseTimer: number | null = null;\n\n      // DEFINE ALL THE CALLBACKS =====\n      function authEventSeen(): void {\n        // Auth event was detected. Resolve this promise and close the extra\n        // window if it's still open.\n        resolve();\n        const closeBrowserTab = cordova.plugins.browsertab?.close;\n        if (typeof closeBrowserTab === 'function') {\n          closeBrowserTab();\n        }\n        // Close inappbrowser embedded webview in iOS7 and 8 case if still\n        // open.\n        if (typeof iabRef?.close === 'function') {\n          iabRef.close();\n        }\n      }\n\n      function resumed(): void {\n        if (onCloseTimer) {\n          // This code already ran; do not rerun.\n          return;\n        }\n\n        onCloseTimer = window.setTimeout(() => {\n          // Wait two seconds after resume then reject.\n          reject(_createError(auth, AuthErrorCode.REDIRECT_CANCELLED_BY_USER));\n        }, REDIRECT_TIMEOUT_MS);\n      }\n\n      function visibilityChanged(): void {\n        if (document?.visibilityState === 'visible') {\n          resumed();\n        }\n      }\n\n      // ATTACH ALL THE LISTENERS =====\n      // Listen for the auth event\n      eventListener.addPassiveListener(authEventSeen);\n\n      // Listen for resume and visibility events\n      document.addEventListener('resume', resumed, false);\n      if (_isAndroid()) {\n        document.addEventListener('visibilitychange', visibilityChanged, false);\n      }\n\n      // SETUP THE CLEANUP FUNCTION =====\n      cleanup = () => {\n        eventListener.removePassiveListener(authEventSeen);\n        document.removeEventListener('resume', resumed, false);\n        document.removeEventListener(\n          'visibilitychange',\n          visibilityChanged,\n          false\n        );\n        if (onCloseTimer) {\n          window.clearTimeout(onCloseTimer);\n        }\n      };\n    });\n  } finally {\n    cleanup();\n  }\n}\n\n/**\n * Checks the configuration of the Cordova environment. This has no side effect\n * if the configuration is correct; otherwise it throws an error with the\n * missing plugin.\n */\nexport function _checkCordovaConfiguration(auth: AuthInternal): void {\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(\n    typeof win?.universalLinks?.subscribe === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-universal-links-plugin-fix'\n    }\n  );\n\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(\n    typeof win?.BuildInfo?.packageName !== 'undefined',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-buildInfo'\n    }\n  );\n\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.openUrl === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n  _assert(\n    typeof win?.cordova?.plugins?.browsertab?.isAvailable === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-browsertab'\n    }\n  );\n\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(\n    typeof win?.cordova?.InAppBrowser?.open === 'function',\n    auth,\n    AuthErrorCode.INVALID_CORDOVA_CONFIGURATION,\n    {\n      missingPlugin: 'cordova-plugin-inappbrowser'\n    }\n  );\n}\n\n/**\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\n * protocol).\n */\nasync function computeSha256(sessionId: string): Promise<string> {\n  const bytes = stringToArrayBuffer(sessionId);\n\n  // TODO: For IE11 crypto has a different name and this operation comes back\n  //       as an object, not a promise. This is the old proposed standard that\n  //       is used by IE11:\n  // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n  const buf = await crypto.subtle.digest('SHA-256', bytes);\n  const arr = Array.from(new Uint8Array(buf));\n  return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n}\n\nfunction stringToArrayBuffer(str: string): Uint8Array {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(\n    /[0-9a-zA-Z]+/.test(str),\n    'Can only convert alpha-numeric strings'\n  );\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystringDecode } from '@firebase/util';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../../core/errors';\nimport { PersistedBlob, PersistenceInternal } from '../../core/persistence';\nimport {\n  KeyName,\n  _persistenceKeyName\n} from '../../core/persistence/persistence_user_manager';\nimport { _createError } from '../../core/util/assert';\nimport { _getInstance } from '../../core/util/instantiator';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthEvent, AuthEventType } from '../../model/popup_redirect';\nimport { browserLocalPersistence } from '../../platform_browser/persistence/local_storage';\n\nconst SESSION_ID_LENGTH = 20;\n\n/** Custom AuthEventManager that adds passive listeners to events */\nexport class CordovaAuthEventManager extends AuthEventManager {\n  private readonly passiveListeners = new Set<(e: AuthEvent) => void>();\n  private resolveInitialized!: () => void;\n  private initPromise = new Promise<void>(resolve => {\n    this.resolveInitialized = resolve;\n  });\n\n  addPassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.add(cb);\n  }\n\n  removePassiveListener(cb: (e: AuthEvent) => void): void {\n    this.passiveListeners.delete(cb);\n  }\n\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect(): void {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n\n  /** Override the onEvent method */\n  onEvent(event: AuthEvent): boolean {\n    this.resolveInitialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n\n  async initialized(): Promise<void> {\n    await this.initPromise;\n  }\n}\n\n/**\n * Generates a (partial) {@link AuthEvent}.\n */\nexport function _generateNewEvent(\n  auth: AuthInternal,\n  type: AuthEventType,\n  eventId: string | null = null\n): AuthEvent {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n\nexport function _savePartialEvent(\n  auth: AuthInternal,\n  event: AuthEvent\n): Promise<void> {\n  return storage()._set(persistenceKey(auth), event as object as PersistedBlob);\n}\n\nexport async function _getAndRemoveEvent(\n  auth: AuthInternal\n): Promise<AuthEvent | null> {\n  const event = (await storage()._get(\n    persistenceKey(auth)\n  )) as AuthEvent | null;\n  if (event) {\n    await storage()._remove(persistenceKey(auth));\n  }\n  return event;\n}\n\nexport function _eventFromPartialAndUrl(\n  partialEvent: AuthEvent,\n  url: string\n): AuthEvent | null {\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError']\n      ? parseJsonOrNull(decodeURIComponent(params['firebaseError']))\n      : null;\n    const code = errorObject?.['code']?.split('auth/')?.[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n\n  return null;\n}\n\nfunction generateSessionId(): string {\n  const chars = [];\n  const allowedChars =\n    '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\n\nfunction storage(): PersistenceInternal {\n  return _getInstance(browserLocalPersistence);\n}\n\nfunction persistenceKey(auth: AuthInternal): string {\n  return _persistenceKeyName(KeyName.AUTH_EVENT, auth.config.apiKey, auth.name);\n}\n\nfunction parseJsonOrNull(json: string): ReturnType<typeof JSON.parse> | null {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n\n// Exported for testing\nexport function _getDeepLinkFromCallback(url: string): string {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id']\n    ? decodeURIComponent(params['deep_link_id'])\n    : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n\n/**\n * Optimistically tries to get search params from a string, or else returns an\n * empty search params object.\n */\nfunction searchParamsOrEmpty(url: string | undefined): Record<string, string> {\n  if (!url?.includes('?')) {\n    return {};\n  }\n\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?')) as Record<string, string>;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../../model/public_types';\nimport { browserSessionPersistence } from '../../platform_browser/persistence/session_storage';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEvent,\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { AuthPopup } from '../../platform_browser/util/popup';\nimport { _createError, _fail } from '../../core/util/assert';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _checkCordovaConfiguration,\n  _generateHandlerUrl,\n  _performRedirect,\n  _validateOrigin,\n  _waitForAppResume\n} from './utils';\nimport {\n  CordovaAuthEventManager,\n  _eventFromPartialAndUrl,\n  _generateNewEvent,\n  _getAndRemoveEvent,\n  _savePartialEvent\n} from './events';\nimport { AuthEventManager } from '../../core/auth/auth_event_manager';\nimport { _getRedirectResult } from '../../platform_browser/strategies/redirect';\nimport {\n  _clearRedirectOutcomes,\n  _overrideRedirectResult\n} from '../../core/strategies/redirect';\nimport { _cordovaWindow } from '../plugins';\n\n/**\n * How long to wait for the initial auth event before concluding no\n * redirect pending\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\n\nclass CordovaPopupRedirectResolver implements PopupRedirectResolverInternal {\n  readonly _redirectPersistence = browserSessionPersistence;\n  readonly _shouldInitProactively = true; // This is lightweight for Cordova\n  private readonly eventManagers = new Map<string, CordovaAuthEventManager>();\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  _completeRedirectFn = _getRedirectResult;\n  _overrideRedirectResult = _overrideRedirectResult;\n\n  async _initialize(auth: AuthInternal): Promise<CordovaAuthEventManager> {\n    const key = auth._key();\n    let manager = this.eventManagers.get(key);\n    if (!manager) {\n      manager = new CordovaAuthEventManager(auth);\n      this.eventManagers.set(key, manager);\n      this.attachCallbackListeners(auth, manager);\n    }\n    return manager;\n  }\n\n  _openPopup(auth: AuthInternal): Promise<AuthPopup> {\n    _fail(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED);\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<void> {\n    _checkCordovaConfiguration(auth);\n    const manager = await this._initialize(auth);\n    await manager.initialized();\n\n    // Reset the persisted redirect states. This does not matter on Web where\n    // the redirect always blows away application state entirely. On Cordova,\n    // the app maintains control flow through the redirect.\n    manager.resetRedirect();\n    _clearRedirectOutcomes();\n\n    await this._originValidation(auth);\n\n    const event = _generateNewEvent(auth, authType, eventId);\n    await _savePartialEvent(auth, event);\n    const url = await _generateHandlerUrl(auth, event, provider);\n    const iabRef = await _performRedirect(url);\n    return _waitForAppResume(auth, manager, iabRef);\n  }\n\n  _isIframeWebStorageSupported(\n    _auth: AuthInternal,\n    _cb: (support: boolean) => unknown\n  ): void {\n    throw new Error('Method not implemented.');\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  private attachCallbackListeners(\n    auth: AuthInternal,\n    manager: AuthEventManager\n  ): void {\n    // Get the global plugins\n    const { universalLinks, handleOpenURL, BuildInfo } = _cordovaWindow();\n\n    const noEventTimeout = setTimeout(async () => {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      await _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }, INITIAL_EVENT_TIMEOUT_MS);\n\n    const universalLinksCb = async (\n      eventData: Record<string, string> | null\n    ): Promise<void> => {\n      // We have an event so we can clear the no event timeout\n      clearTimeout(noEventTimeout);\n\n      const partialEvent = await _getAndRemoveEvent(auth);\n      let finalEvent: AuthEvent | null = null;\n      if (partialEvent && eventData?.['url']) {\n        finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n      }\n\n      // If finalEvent is never filled, trigger with no event\n      manager.onEvent(finalEvent || generateNoEvent());\n    };\n\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (\n      typeof universalLinks !== 'undefined' &&\n      typeof universalLinks.subscribe === 'function'\n    ) {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = async url => {\n      if (url.toLowerCase().startsWith(packagePrefix)) {\n        // We want this intentionally to float\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        universalLinksCb({ url });\n      }\n      // Call the developer's handler if it is present.\n      if (typeof existingHandleOpenURL === 'function') {\n        try {\n          existingHandleOpenURL(url);\n        } catch (e) {\n          // This is a developer error. Don't stop the flow of the SDK.\n          console.error(e);\n        }\n      }\n    };\n  }\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\n * based applications.\n *\n * @public\n */\nexport const cordovaPopupRedirectResolver: PopupRedirectResolver =\n  CordovaPopupRedirectResolver;\n\nfunction generateNoEvent(): AuthEvent {\n  return {\n    type: AuthEventType.UNKNOWN,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(AuthErrorCode.NO_AUTH_EVENT)\n  };\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": ["STORAGE_AVAILABLE_KEY", "_isMobileBrowser", "_isIE10", "jsHelpers._generateCallbackName", "Delay", "_window", "_assert", "isV2", "_createError", "jsHelpers._recaptchaV2ScriptUrl", "querystring", "jsHelpers._loadJS", "MockReCaptcha", "_castAuth", "_isHttpOrHttps", "_isWorker", "getRecaptchaParams", "PhoneAuthCredential", "_isFirebaseServerApp", "_serverAppCurrentUserOperationNotSupportedError", "getModularInstance", "signInWithCredential", "_assertLinkedStatus", "linkWithCredential", "reauthenticateWithCredential", "_initializeRecaptchaConfig", "FAKE_TOKEN", "startEnrollPhoneMfa", "handleRecaptchaFlow", "startSignInPhoneMfa", "sendPhoneVerificationCode", "_link", "_getInstance", "AuthCredential", "signInWithIdp", "_signInWithCredential", "_reauthenticate", "_linkUser", "_fail", "debugAssert", "_assertInstanceOf", "FederatedAuthProvider", "_generateEventId", "_persistenceKeyName", "_performApiRequest", "_validate<PERSON><PERSON><PERSON>", "_getCurrentUrl", "js._generateCallbackName", "js\n                ._loadJS", "js._gapiScriptUrl", "_emulatorUrl", "SDK_VERSION", "gapiLoader._loadGapi", "getUA", "_isChromeIOS", "_isFirefox", "_isIOSStandalone", "isEmpty", "BaseOAuthProvider", "_setWindowLocation", "_is<PERSON><PERSON><PERSON>", "_isIOS", "MultiFactorAssertionImpl", "finalizeEnrollPhoneMfa", "finalizeSignInPhoneMfa", "getExperimentalSetting", "app", "getApp", "_get<PERSON><PERSON><PERSON>", "initializeAuth", "indexedDBLocalPersistence", "beforeAuthStateChanged", "onIdTokenChanged", "getDefaultEmulatorHost", "connectAuthEmulator", "_setExternalJSProvider", "registerAuth", "_isAndroid", "_isIOS7Or8", "querystringDecode"], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACU,MAAA,QAAQ,GAAG;;AAEtB,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,MAAM;EACH;AAEX;;;;AAIG;AACU,MAAA,UAAU,GAAG;;AAExB,IAAA,QAAQ,EAAE,cAAc;;AAExB,IAAA,MAAM,EAAE,YAAY;;AAEpB,IAAA,MAAM,EAAE,YAAY;;AAEpB,IAAA,QAAQ,EAAE,UAAU;;AAEpB,IAAA,KAAK,EAAE,OAAO;;AAEd,IAAA,OAAO,EAAE,aAAa;EACb;AAEX;;;;AAIG;AACU,MAAA,YAAY,GAAG;;AAE1B,IAAA,UAAU,EAAE,WAAW;;AAEvB,IAAA,cAAc,EAAE,UAAU;;AAE1B,IAAA,QAAQ,EAAE,cAAc;;AAExB,IAAA,MAAM,EAAE,YAAY;;AAEpB,IAAA,MAAM,EAAE,YAAY;;AAEpB,IAAA,KAAK,EAAE,OAAO;;AAEd,IAAA,OAAO,EAAE,aAAa;EACb;AAEX;;;;AAIG;AACU,MAAA,aAAa,GAAG;;AAE3B,IAAA,IAAI,EAAE,MAAM;;AAEZ,IAAA,cAAc,EAAE,gBAAgB;;AAEhC,IAAA,OAAO,EAAE,QAAQ;EACR;AAEX;;;;AAIG;AACU,MAAA,mBAAmB,GAAG;;AAEjC,IAAA,YAAY,EAAE,cAAc;;AAE5B,IAAA,cAAc,EAAE,gBAAgB;;AAEhC,IAAA,aAAa,EAAE,eAAe;;AAE9B,IAAA,6BAA6B,EAAE,+BAA+B;;AAE9D,IAAA,uBAAuB,EAAE,yBAAyB;;AAElD,IAAA,YAAY,EAAE,cAAc;;;ACrG9B;;;;;;;;;;;;;;;AAeG;AAQH;AACA;AACA;MAEsB,uBAAuB,CAAA;IAC3C,WACqB,CAAA,gBAA+B,EACzC,IAAqB,EAAA;QADX,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAe;QACzC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAiB;KAC5B;IAEJ,YAAY,GAAA;AACV,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjB,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAC/B;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,CAACA,8BAAqB,EAAE,GAAG,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAACA,8BAAqB,CAAC,CAAC;AAC/C,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC9B;AAAC,QAAA,OAAA,EAAA,EAAM;AACN,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;KACF;IAED,IAAI,CAAC,GAAW,EAAE,KAAuB,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,IAAI,CAA6B,GAAW,EAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvC,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;KACxD;AAED,IAAA,OAAO,CAAC,GAAW,EAAA;AACjB,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,IAAc,OAAO,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;KAChC;AACF;;AChED;;;;;;;;;;;;;;;AAeG;AAaH;AACO,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAEzC;AACA,MAAM,6BAA6B,GAAG,EAAE,CAAC;AAEzC,MAAM,uBACJ,SAAQ,uBAAuB,CAAA;AAK/B,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,MAAM,CAAC,YAAY,sCAAwB,CAAC;AAGzC,QAAA,IAAA,CAAA,iBAAiB,GAAG,CACnC,KAAmB,EACnB,IAAc,KACL,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3B,IAAS,CAAA,SAAA,GAA8C,EAAE,CAAC;QAC1D,IAAU,CAAA,UAAA,GAAkC,EAAE,CAAC;;;QAGxD,IAAS,CAAA,SAAA,GAAe,IAAI,CAAC;;QAGpB,IAAiB,CAAA,iBAAA,GAAGC,yBAAgB,EAAE,CAAC;QAC/C,IAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;KAdrC;AAgBO,IAAA,iBAAiB,CACvB,EAA2E,EAAA;;AAG3E,QAAA,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;YAE7C,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;;AAGtC,YAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,gBAAA,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC7B;SACF;KACF;AAEO,IAAA,cAAc,CAAC,KAAmB,EAAE,IAAI,GAAG,KAAK,EAAA;;AAEtD,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,iBAAiB,CACpB,CAAC,GAAW,EAAE,SAAwB,EAAE,QAAuB,KAAI;AACjE,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACtC,aAAC,CACF,CAAC;YACF,OAAO;SACR;AAED,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;;;QAItB,IAAI,IAAI,EAAE;;;YAGR,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;aAAM;;;YAGL,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;QAED,MAAM,gBAAgB,GAAG,MAAW;;;YAGlC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;;;gBAGjD,OAAO;aACR;AACD,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACzC,SAAC,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IACEC,gBAAO,EAAE;YACT,WAAW,KAAK,KAAK,CAAC,QAAQ;AAC9B,YAAA,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,EACjC;;;;;AAKA,YAAA,UAAU,CAAC,gBAAgB,EAAE,6BAA6B,CAAC,CAAC;SAC7D;aAAM;AACL,YAAA,gBAAgB,EAAE,CAAC;SACpB;KACF;IAEO,eAAe,CAAC,GAAW,EAAE,KAAoB,EAAA;AACvD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAC5C,gBAAA,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;aAC7C;SACF;KACF;IAEO,YAAY,GAAA;QAClB,IAAI,CAAC,WAAW,EAAE,CAAC;AAEnB,QAAA,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,MAAK;YAChC,IAAI,CAAC,iBAAiB,CACpB,CAAC,GAAW,EAAE,QAAuB,EAAE,QAAuB,KAAI;AAChE,gBAAA,IAAI,CAAC,cAAc,CACjB,IAAI,YAAY,CAAC,SAAS,EAAE;oBAC1B,GAAG;oBACH,QAAQ;oBACR,QAAQ;iBACT,CAAC;2BACS,IAAI,CAChB,CAAC;AACJ,aAAC,CACF,CAAC;SACH,EAAE,oBAAoB,CAAC,CAAC;KAC1B;IAEO,WAAW,GAAA;AACjB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;KACF;IAEO,cAAc,GAAA;QACpB,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5D;IAEO,cAAc,GAAA;QACpB,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC/D;IAED,YAAY,CAAC,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;;;;;AAK5C,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;aACrB;iBAAM;gBACL,IAAI,CAAC,cAAc,EAAE,CAAC;aACvB;SACF;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;;AAEhC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KACnC;IAED,eAAe,CAAC,GAAW,EAAE,QAA8B,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAClC,gBAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aAC5B;SACF;AAED,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;KACF;;AAID,IAAA,MAAM,IAAI,CAAC,GAAW,EAAE,KAAuB,EAAA;QAC7C,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KAC9C;IAED,MAAM,IAAI,CAA6B,GAAW,EAAA;QAChD,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,CAAI,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7C,QAAA,OAAO,KAAK,CAAC;KACd;IAED,MAAM,OAAO,CAAC,GAAW,EAAA;AACvB,QAAA,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACzB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KAC7B;;AAxLM,uBAAI,CAAA,IAAA,GAAY,OAAZ,CAAoB;AA2LjC;;;;;AAKG;AACI,MAAM,uBAAuB,GAAgB;;ACvOpD;;;;;;;;;;;;;;;AAeG;AAKH,MAAM,mBAAmB,GAAG,IAAK,CAAC;AASlC;AACA,SAAS,iBAAiB,CAAC,IAAY,EAAA;;IACrC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAChE,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,WAAW,CAAA,QAAA,CAAU,CAAC,CAAC;AACjD,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;AACrD,CAAC;AAED;AACA,SAAS,aAAa,CAAC,GAAW,EAAA;;;;IAIhC,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC;IACvD,OAAO,CAAA,EAAG,SAAS,GAAG,QAAQ,GAAG,SAAS,YAAY,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;AAC5E,CAAC;MAEY,iBAAiB,CAAA;AAA9B,IAAA,WAAA,GAAA;AAEW,QAAA,IAAA,CAAA,IAAI,GAA0B,QAAA,8BAAA;AACvC,QAAA,IAAA,CAAA,oBAAoB,GAA0C,IAAI,GAAG,EAAE,CAAC;KA0GzE;;AAvGC,IAAA,eAAe,CAAC,WAAmB,EAAA;AACjC,QAAA,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;AAC/B,YAAA,OAAO,WAAW,CAAC;SACpB;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAA,EAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAA,YAAA,CAAc,CAAC,CAAC;QAC7D,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACjD,QAAA,OAAO,GAAG,CAAC;KACZ;;;;AAKD,IAAA,MAAM,YAAY,GAAA;;QAChB,IAAI,OAAO,eAAe,KAAK,SAAS,IAAI,CAAC,eAAe,EAAE;AAC5D,YAAA,OAAO,KAAK,CAAC;SACd;QACD,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACvE,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,MAAA,SAAS,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;KACxC;;AAGD,IAAA,MAAM,IAAI,CAAC,IAAY,EAAE,MAAwB,EAAA;QAC/C,OAAO;KACR;;IAGD,MAAM,IAAI,CAA6B,GAAW,EAAA;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,IAAI,MAAM,CAAC,WAAW,EAAE;YACtB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,YAAA,OAAO,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,KAAU,CAAC;SAC3B;AACD,QAAA,OAAO,iBAAiB,CAAC,IAAI,CAAM,CAAC;KACrC;;IAGD,MAAM,OAAO,CAAC,GAAW,EAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;YACxB,OAAO;SACR;;;;QAID,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,QAAQ,CAAC,MAAM,GAAG,CAAG,EAAA,IAAI,4EAA4E,CAAC;AACtG,QAAA,MAAM,KAAK,CAAC,CAAA,YAAA,CAAc,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;KAC1E;;IAGD,YAAY,CAAC,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;YACxB,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,IAAI,MAAM,CAAC,WAAW,EAAE;AACtB,YAAA,MAAM,EAAE,IAAI,CAAC,KAAwB,KAAU;AAC7C,gBAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACtC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAC/B,CAAC;gBACF,IAAI,aAAa,EAAE;AACjB,oBAAA,QAAQ,CAAC,aAAa,CAAC,KAAyB,CAAC,CAAC;iBACnD;AACD,gBAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACtC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAC/B,CAAC;gBACF,IAAI,aAAa,EAAE;oBACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChB;AACH,aAAC,CAAkB,CAAC;AACpB,YAAA,MAAM,WAAW,GAAG,MAClB,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAmB,CAAC,CAAC;SAC3E;AACD,QAAA,IAAI,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACxC,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAK;AAChC,YAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC7C,YAAA,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,QAAQ,CAAC,YAAuC,CAAC,CAAC;gBAClD,SAAS,GAAG,YAAY,CAAC;aAC1B;SACF,EAAE,mBAAmB,CAAC,CAAC;QACxB,MAAM,WAAW,GAAG,MAAY,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;KACtD;IAED,eAAe,CAAC,IAAY,EAAE,QAA8B,EAAA;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO;SACR;AACD,QAAA,WAAW,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC5C;;AA3GM,iBAAI,CAAA,IAAA,GAAa,QAAb,CAAsB;AA8GnC;;;;;;;;AAQG;AACI,MAAM,wBAAwB,GAAgB;;ACrKrD;;;;;;;;;;;;;;;AAeG;AAWH,MAAM,yBACJ,SAAQ,uBAAuB,CAAA;AAK/B,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,MAAM,CAAC,cAAc,0CAA0B,CAAC;KAC7D;IAED,YAAY,CAAC,IAAY,EAAE,SAA+B,EAAA;;QAExD,OAAO;KACR;IAED,eAAe,CAAC,IAAY,EAAE,SAA+B,EAAA;;QAE3D,OAAO;KACR;;AAdM,yBAAI,CAAA,IAAA,GAAc,SAAS,CAAC;AAiBrC;;;;;AAKG;AACI,MAAM,yBAAyB,GAAgB;;ACrDtD;;;;;;;;;;;;;;;AAeG;AAaH;AACA;AACO,MAAM,gBAAgB,GAAGC,8BAA+B,CAAC,KAAK,CAAC,CAAC;AACvE,MAAM,qBAAqB,GAAG,IAAIC,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAatD;;AAEG;MACU,mBAAmB,CAAA;AAAhC,IAAA,WAAA,GAAA;;QACU,IAAY,CAAA,YAAA,GAAG,EAAE,CAAC;QAClB,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;AACpB;;;;AAIG;AACc,QAAA,IAAA,CAAA,uBAAuB,GAAG,CAAC,EAAC,CAAA,EAAA,GAAAC,gBAAO,EAAE,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,CAAC;KAqE3E;AAnEC,IAAA,IAAI,CAAC,IAAkB,EAAE,EAAE,GAAG,EAAE,EAAA;QAC9BC,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,IAAI,sDAA+B,CAAC;AAErE,QAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,IAAIC,aAAI,CAACF,gBAAO,EAAE,CAAC,UAAU,CAAC,EAAE;YACnE,OAAO,OAAO,CAAC,OAAO,CAACA,gBAAO,EAAE,CAAC,UAAwB,CAAC,CAAC;SAC5D;QACD,OAAO,IAAI,OAAO,CAAY,CAAC,OAAO,EAAE,MAAM,KAAI;YAChD,MAAM,cAAc,GAAGA,gBAAO,EAAE,CAAC,UAAU,CAAC,MAAK;AAC/C,gBAAA,MAAM,CAACG,qBAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;AACnE,aAAC,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC,CAAC;AAEhC,YAAAH,gBAAO,EAAE,CAAC,gBAAgB,CAAC,GAAG,MAAK;AACjC,gBAAAA,gBAAO,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AACvC,gBAAA,OAAOA,gBAAO,EAAE,CAAC,gBAAgB,CAAC,CAAC;AAEnC,gBAAA,MAAM,SAAS,GAAGA,gBAAO,EAAE,CAAC,UAAuB,CAAC;gBAEpD,IAAI,CAAC,SAAS,IAAI,CAACE,aAAI,CAAC,SAAS,CAAC,EAAE;AAClC,oBAAA,MAAM,CAACC,qBAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;oBACzD,OAAO;iBACR;;;AAID,gBAAA,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gBAChC,SAAS,CAAC,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,KAAI;oBACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,oBAAA,OAAO,QAAQ,CAAC;AAClB,iBAAC,CAAC;AAEF,gBAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC;AACrB,aAAC,CAAC;YAEF,MAAM,GAAG,GAAG,CAAG,EAAAC,8BAA+B,EAAE,CAAI,CAAA,EAAAC,gBAAW,CAAC;AAC9D,gBAAA,MAAM,EAAE,gBAAgB;AACxB,gBAAA,MAAM,EAAE,UAAU;gBAClB,EAAE;AACH,aAAA,CAAC,EAAE,CAAC;YAELC,gBAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,MAAK;gBAChC,YAAY,CAAC,cAAc,CAAC,CAAC;AAC7B,gBAAA,MAAM,CAACH,qBAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;AAC3D,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB;AAEO,IAAA,wBAAwB,CAAC,EAAU,EAAA;;;;;;;;;QAQzC,QACE,CAAC,EAAC,CAAA,EAAA,GAAAH,gBAAO,EAAE,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,CAAA;AAC9B,aAAC,EAAE,KAAK,IAAI,CAAC,YAAY;gBACvB,IAAI,CAAC,OAAO,GAAG,CAAC;AAChB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,EAC/B;KACH;AACF,CAAA;AAED,SAAS,mBAAmB,CAAC,EAAU,EAAA;AACrC,IAAA,OAAO,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC;MAEY,uBAAuB,CAAA;IAClC,MAAM,IAAI,CAAC,IAAkB,EAAA;AAC3B,QAAA,OAAO,IAAIO,sBAAa,CAAC,IAAI,CAAC,CAAC;KAChC;AAED,IAAA,kBAAkB,MAAW;AAC9B;;ACxID;;;;;;;;;;;;;;;AAeG;AAmBI,MAAM,uBAAuB,GAAG,WAAW,CAAC;AAEnD,MAAM,cAAc,GAAwB;AAC1C,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,OAAO;CACd,CAAC;AAIF;;;;;;;AAOG;MACU,iBAAiB,CAAA;AAoB5B;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,WAAA,CACE,UAAgB,EAChB,aAAmC,EAClB,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACZ,cAAc,CAClB,EAAA;QAFgB,IAAU,CAAA,UAAA,GAAV,UAAU,CAE1B;AA3CH;;;;;AAKG;QACM,IAAI,CAAA,IAAA,GAAG,uBAAuB,CAAC;QAChC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAQ,CAAA,QAAA,GAAkB,IAAI,CAAC;AAGtB,QAAA,IAAA,CAAA,oBAAoB,GAAG,IAAI,GAAG,EAAiB,CAAC;QACzD,IAAa,CAAA,aAAA,GAA2B,IAAI,CAAC;QAK7C,IAAS,CAAA,SAAA,GAAqB,IAAI,CAAC;AA4BzC,QAAA,IAAI,CAAC,IAAI,GAAGC,kBAAS,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC;QACxDP,gBAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;AACF,QAAA,MAAM,SAAS,GACb,OAAO,aAAa,KAAK,QAAQ;AAC/B,cAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;cACtC,aAAa,CAAC;AACpB,QAAAA,gBAAO,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAE5D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE5E,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC;cACxE,IAAI,uBAAuB,EAAE;AAC/B,cAAE,IAAI,mBAAmB,EAAE,CAAC;QAE9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;;KAE9B;AAED;;;;AAIG;AACH,IAAA,MAAM,MAAM,GAAA;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AAC/B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE9C,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,QAAQ,CAAC;SACjB;AAED,QAAA,OAAO,IAAI,OAAO,CAAS,OAAO,IAAG;AACnC,YAAA,MAAM,WAAW,GAAG,CAAC,KAAa,KAAU;gBAC1C,IAAI,CAAC,KAAK,EAAE;AACV,oBAAA,OAAO;iBACR;AACD,gBAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC9C,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI;YACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;QAAC,OAAO,CAAC,EAAE;;;;AAIV,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC1B;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC,IAAG;AACtD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,MAAM,CAAC,CAAC;AACV,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;IAGD,MAAM,GAAA;QACJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClD;KACF;AAED;;AAEG;IACH,KAAK,GAAA;QACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAG;AACvC,gBAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,aAAC,CAAC,CAAC;SACJ;KACF;IAEO,qBAAqB,GAAA;AAC3B,QAAAA,gBAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC3E,QAAAA,gBAAO,CACL,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,EACnD,IAAI,CAAC,IAAI,sDAEV,CAAC;QACFA,gBAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;KACH;AAEO,IAAA,iBAAiB,CACvB,QAA4C,EAAA;QAE5C,OAAO,KAAK,IAAG;AACb,YAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/D,YAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACjB;AAAM,iBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACvC,gBAAA,MAAM,UAAU,GAAGD,gBAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;AACvC,gBAAA,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;oBACpC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACnB;aACF;AACH,SAAC,CAAC;KACH;IAEO,kBAAkB,GAAA;QACxBC,gBAAO,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;KACnE;AAEO,IAAA,MAAM,iBAAiB,GAAA;AAC7B,QAAA,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,gBAAA,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBACvC,SAAS,GAAG,eAAe,CAAC;aAC7B;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAChD,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,IAAI,GAAA;AAChB,QAAAA,gBAAO,CACLQ,uBAAc,EAAE,IAAI,CAACC,kBAAS,EAAE,EAChC,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAEV,CAAC;QAEF,MAAM,QAAQ,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAC/C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,CACpC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAMC,2BAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD,QAAAV,gBAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAC1D,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;KACnC;IAEO,oBAAoB,GAAA;QAC1BA,gBAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AACF,CAAA;AAED,SAAS,QAAQ,GAAA;IACf,IAAI,QAAQ,GAAwB,IAAI,CAAC;AACzC,IAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;AACjC,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;AACtC,YAAA,OAAO,EAAE,CAAC;YACV,OAAO;SACR;;;;AAKD,QAAA,QAAQ,GAAG,MAAM,OAAO,EAAE,CAAC;AAC3B,QAAA,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,KAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;QACX,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;AAED,QAAA,MAAM,CAAC,CAAC;AACV,KAAC,CAAC,CAAC;AACL;;AC1SA;;;;;;;;;;;;;;;AAeG;AAiEH,MAAM,sBAAsB,CAAA;IAC1B,WACW,CAAA,cAAsB,EACd,cAAsC,EAAA;QAD9C,IAAc,CAAA,cAAA,GAAd,cAAc,CAAQ;QACd,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;KACrD;AAEJ,IAAA,OAAO,CAAC,gBAAwB,EAAA;AAC9B,QAAA,MAAM,cAAc,GAAGW,4BAAmB,CAAC,iBAAiB,CAC1D,IAAI,CAAC,cAAc,EACnB,gBAAgB,CACjB,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;KAC5C;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;AACI,eAAe,qBAAqB,CACzC,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,IAAIC,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAGN,kBAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,EACZ,WAAW,EACXO,uBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpDC,6BAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,CACzC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;AAWG;AACI,eAAe,mBAAmB,CACvC,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,MAAM,YAAY,GAAGD,uBAAkB,CAAC,IAAI,CAAiB,CAAC;AAC9D,IAAA,MAAME,4BAAmB,CAAC,KAAK,EAAE,YAAY,iCAAmB,CAAC;AACjE,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACXF,uBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpDG,2BAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,CACvC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACI,eAAe,6BAA6B,CACjD,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,MAAM,YAAY,GAAGH,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAIF,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC;KACH;AACD,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACXC,uBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpDI,qCAA4B,CAAC,YAAY,EAAE,IAAI,CAAC,CACjD,CAAC;AACJ,CAAC;AAOD;;;AAGG;AACI,eAAe,kBAAkB,CACtC,IAAkB,EAClB,OAAkC,EAClC,QAAsC,EAAA;;AAEtC,IAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE;AAC/B,QAAA,IAAI;AACF,YAAA,MAAMC,mCAA0B,CAAC,IAAI,CAAC,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;;;;;AAKd,YAAA,OAAO,CAAC,GAAG,CACT,6FAA6F,CAC9F,CAAC;SACH;KACF;AAED,IAAA,IAAI;AACF,QAAA,IAAI,gBAAkC,CAAC;AAEvC,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC/B,YAAA,gBAAgB,GAAG;AACjB,gBAAA,WAAW,EAAE,OAAO;aACrB,CAAC;SACH;aAAM;YACL,gBAAgB,GAAG,OAAO,CAAC;SAC5B;AAED,QAAA,IAAI,SAAS,IAAI,gBAAgB,EAAE;AACjC,YAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAiC,CAAC;AAEnE,YAAA,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACrCnB,gBAAO,CACL,OAAO,CAAC,IAAI,mDACZ,IAAI,sDAEL,CAAC;AAEF,gBAAA,MAAM,8BAA8B,GAAmC;oBACrE,OAAO,EAAE,OAAO,CAAC,UAAU;AAC3B,oBAAA,mBAAmB,EAAE;wBACnB,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,wBAAA,UAAU,EAAyB,iBAAA;AACpC,qBAAA;iBACF,CAAC;gBAEF,MAAM,iCAAiC,GAGnC,OACF,YAA0B,EAC1B,OAAuC,KACrC;;oBAEF,IAAI,OAAO,CAAC,mBAAmB,CAAC,eAAe,KAAKoB,mBAAU,EAAE;AAC9D,wBAAApB,gBAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;wBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,wBAAA,OAAOqB,4BAAmB,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;qBAClE;AACD,oBAAA,OAAOA,4BAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACpD,iBAAC,CAAC;gBAEF,MAAM,+BAA+B,GACnCC,4BAAmB,CACjB,IAAI,EACJ,8BAA8B,EAAA,kBAAA,+CAE9B,iCAAiC,EAAA,gBAAA,4CAElC,CAAC;gBAEJ,MAAM,QAAQ,GAAG,MAAM,+BAA+B,CAAC,KAAK,CAAC,KAAK,IAAG;AACnE,oBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC;aAC9C;iBAAM;gBACLtB,gBAAO,CACL,OAAO,CAAC,IAAI,oDACZ,IAAI,sDAEL,CAAC;gBACF,MAAM,eAAe,GACnB,CAAA,CAAA,EAAA,GAAA,gBAAgB,CAAC,eAAe,0CAAE,GAAG;oBACrC,gBAAgB,CAAC,cAAc,CAAC;AAClC,gBAAAA,gBAAO,CAAC,eAAe,EAAE,IAAI,mEAAiC,CAAC;AAE/D,gBAAA,MAAM,0BAA0B,GAA+B;oBAC7D,oBAAoB,EAAE,OAAO,CAAC,UAAU;oBACxC,eAAe;AACf,oBAAA,eAAe,EAAE;AACf,wBAAA,UAAU,EAAyB,iBAAA;AACpC,qBAAA;iBACF,CAAC;gBAEF,MAAM,iCAAiC,GAGnC,OACF,YAA0B,EAC1B,OAAmC,KACjC;;oBAEF,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,KAAKoB,mBAAU,EAAE;AAC1D,wBAAApB,gBAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;wBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,wBAAA,OAAOuB,4BAAmB,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;qBAClE;AACD,oBAAA,OAAOA,4BAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACpD,iBAAC,CAAC;gBAEF,MAAM,2BAA2B,GAC/BD,4BAAmB,CACjB,IAAI,EACJ,0BAA0B,EAAA,cAAA,2CAE1B,iCAAiC,EAAA,gBAAA,4CAElC,CAAC;gBAEJ,MAAM,QAAQ,GAAG,MAAM,2BAA2B,CAAC,KAAK,CAAC,KAAK,IAAG;AAC/D,oBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC;aAC/C;SACF;aAAM;AACL,YAAA,MAAM,gCAAgC,GACpC;gBACE,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,gBAAA,UAAU,EAAyB,iBAAA;aACpC,CAAC;YAEJ,MAAM,uCAAuC,GAGzC,OACF,YAA0B,EAC1B,OAAyC,KACvC;;AAEF,gBAAA,IAAI,OAAO,CAAC,eAAe,KAAKF,mBAAU,EAAE;AAC1C,oBAAApB,gBAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;oBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,oBAAA,OAAOwB,kCAAyB,CAC9B,YAAY,EACZ,sBAAsB,CACvB,CAAC;iBACH;AACD,gBAAA,OAAOA,kCAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAC1D,aAAC,CAAC;YAEF,MAAM,iCAAiC,GACrCF,4BAAmB,CACjB,IAAI,EACJ,gCAAgC,EAAA,sBAAA,mDAEhC,uCAAuC,EAAA,gBAAA,4CAExC,CAAC;YAEJ,MAAM,QAAQ,GAAG,MAAM,iCAAiC,CAAC,KAAK,CAAC,KAAK,IAAG;AACrE,gBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,WAAW,CAAC;SAC7B;KACF;YAAS;AACR,QAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,MAAM,EAAE,CAAC;KACpB;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACI,eAAe,iBAAiB,CACrC,IAAU,EACV,UAA+B,EAAA;AAE/B,IAAA,MAAM,YAAY,GAAGR,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAIF,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC;KACH;AACD,IAAA,MAAMY,cAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AACxC,CAAC;AAED;AACO,eAAe,sBAAsB,CAC1C,IAAkB,EAClB,OAAU,EACV,mBAAgD,EAAA;IAEhDzB,gBAAO,CACL,mBAAmB,CAAC,IAAI,KAAK,uBAAuB,EACpD,IAAI,EAAA,gBAAA,oCAEL,CAAC;AAEF,IAAA,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE,CAAC;IAE5DA,gBAAO,CACL,OAAO,gBAAgB,KAAK,QAAQ,EACpC,IAAI,sDAEL,CAAC;AAEF,IAAA,MAAM,UAAU,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAE,CAAC;AAElC,IAAA,IAAI,qBAAqB,IAAI,UAAU,EAAE;AACvC,QAAA,MAAM,WAAW,GACf,UACD,CAAC,mBAAmB,CAAC,WAAW,CAAC;AAClC,QAAA,MAAM,eAAe,GACnB,UACD,CAAC,mBAAmB,CAAC,eAAe,CAAC;QACtC,MAAM,UAAU,GAAI,UAAwD;aACzE,mBAAmB,CAAC,UAAU,CAAC;AAClC,QAAA,MAAM,gBAAgB,GACpB,UACD,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;AAEvC,QAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AACxB,YAAA,qBAAqB,EAAE;gBACrB,WAAW;AACX,gBAAA,cAAc,EAAE,gBAAgB;gBAChC,eAAe;gBACf,UAAU;gBACV,gBAAgB;AACjB,aAAA;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACnB;AAAM,SAAA,IAAI,iBAAiB,IAAI,UAAU,EAAE;AAC1C,QAAA,MAAM,eAAe,GACnB,UACD,CAAC,eAAe,CAAC,eAAe,CAAC;QAClC,MAAM,UAAU,GAAI,UAAoD;aACrE,eAAe,CAAC,UAAU,CAAC;AAC9B,QAAA,MAAM,gBAAgB,GACpB,UACD,CAAC,eAAe,CAAC,gBAAgB,CAAC;AAEnC,QAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AACxB,YAAA,iBAAiB,EAAE;AACjB,gBAAA,cAAc,EAAE,gBAAgB;gBAChC,eAAe;gBACf,UAAU;gBACV,gBAAgB;AACjB,aAAA;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACnB;SAAM;QACL,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CAAC,CAAC;AAClE,QAAA,OAAO,UAAU,CAAC;KACnB;AACH;;ACrhBA;;;;;;;;;;;;;;;AAeG;AAqBH;;;;;;;;;;;;;;;;;;AAkBG;MACU,iBAAiB,CAAA;AAU5B;;;AAGG;AACH,IAAA,WAAA,CAAY,IAAU,EAAA;;AAPb,QAAA,IAAA,CAAA,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAQlD,QAAA,IAAI,CAAC,IAAI,GAAGO,kBAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;IACH,iBAAiB,CACf,YAAuC,EACvC,mBAAyC,EAAA;AAEzC,QAAA,OAAO,kBAAkB,CACvB,IAAI,CAAC,IAAI,EACT,YAAY,EACZO,uBAAkB,CAAC,mBAAkD,CAAC,CACvE,CAAC;KACH;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACH,IAAA,OAAO,UAAU,CACf,cAAsB,EACtB,gBAAwB,EAAA;QAExB,OAAOH,4BAAmB,CAAC,iBAAiB,CAC1C,cAAc,EACd,gBAAgB,CACjB,CAAC;KACH;AAED;;;AAGG;IACH,OAAO,oBAAoB,CACzB,cAA8B,EAAA;QAE9B,MAAM,UAAU,GAAG,cAAwC,CAAC;AAC5D,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;KACjE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;IACH,OAAO,mBAAmB,CAAC,KAAoB,EAAA;AAC7C,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,EAChD,KAAK,CAAC,UAAU,IAAI,EAAE,EACxB,CAAC;KACH;AAEO,IAAA,OAAO,0BAA0B,CAAC,EACxC,cAAc,EAAE,aAAa,EACL,EAAA;QACxB,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GACnC,aAA8C,CAAC;AACjD,QAAA,IAAI,WAAW,IAAI,cAAc,EAAE;YACjC,OAAOA,4BAAmB,CAAC,kBAAkB,CAC3C,WAAW,EACX,cAAc,CACf,CAAC;SACH;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAlKD;AACgB,iBAAA,CAAA,WAAW,kCAA6B;AACxD;AACgB,iBAAA,CAAA,oBAAoB;;AC3DtC;;;;;;;;;;;;;;;AAeG;AASH;;;;AAIG;AACa,SAAA,oBAAoB,CAClC,IAAkB,EAClB,gBAAmD,EAAA;IAEnD,IAAI,gBAAgB,EAAE;AACpB,QAAA,OAAOe,qBAAY,CAAC,gBAAgB,CAAC,CAAC;KACvC;AAED,IAAA1B,gBAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,sDAA+B,CAAC;IAEzE,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACrC;;ACxCA;;;;;;;;;;;;;;;AAeG;AAiCH,MAAM,aAAc,SAAQ2B,uBAAc,CAAA;AACxC,IAAA,WAAA,CAAqB,MAAqB,EAAA;AACxC,QAAA,KAAK,oEAAsC,CAAC;QADzB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;KAEzC;AAED,IAAA,mBAAmB,CAAC,IAAkB,EAAA;QACpC,OAAOC,sBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD;IAED,cAAc,CACZ,IAAkB,EAClB,OAAe,EAAA;QAEf,OAAOA,sBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;KAC5D;AAED,IAAA,4BAA4B,CAAC,IAAkB,EAAA;QAC7C,OAAOA,sBAAa,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;KACrD;AAEO,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AACvC,QAAA,MAAM,OAAO,GAAyB;AACpC,YAAA,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;AAClC,YAAA,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;AAChC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC9B,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;AACtC,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,mBAAmB,EAAE,IAAI;SAC1B,CAAC;QAEF,IAAI,OAAO,EAAE;AACX,YAAA,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;SAC3B;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;AAErB,IAAA,OAAOC,8BAAqB,CAC1B,MAAM,CAAC,IAAI,EACX,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACY,CAAC;AACvC,CAAC;AAEK,SAAU,OAAO,CACrB,MAAqB,EAAA;AAErB,IAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAC9B,IAAA7B,gBAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,IAAA,OAAO8B,wBAAe,CACpB,IAAI,EACJ,IAAI,aAAa,CAAC,MAAM,CAAC,EACzB,MAAM,CAAC,eAAe,CACvB,CAAC;AACJ,CAAC;AAEM,eAAe,KAAK,CACzB,MAAqB,EAAA;AAErB,IAAA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAC9B,IAAA9B,gBAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;AAClD,IAAA,OAAO+B,cAAS,CAAC,IAAI,EAAE,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;AAC5E;;ACnHA;;;;;;;;;;;;;;;AAeG;AA4BH;;;AAGG;MACmB,8BAA8B,CAAA;IASlD,WACqB,CAAA,IAAkB,EACrC,MAAuC,EACpB,QAAuC,EAChD,IAAmB,EACV,eAAA,GAAkB,KAAK,EAAA;QAJvB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;QAElB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA+B;QAChD,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;QACV,IAAe,CAAA,eAAA,GAAf,eAAe,CAAQ;QAXpC,IAAc,CAAA,cAAA,GAA0B,IAAI,CAAC;QAC7C,IAAY,CAAA,YAAA,GAAwB,IAAI,CAAC;AAY/C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;KACzD;IAID,OAAO,GAAA;QACL,OAAO,IAAI,OAAO,CAChB,OAAO,OAAO,EAAE,MAAM,KAAI;YACxB,IAAI,CAAC,cAAc,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAE1C,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/D,gBAAA,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB,gBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;aAC1C;YAAC,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,CAAC,MAAM,CAAC,CAAU,CAAC,CAAC;aACzB;AACH,SAAC,CACF,CAAC;KACH;IAED,MAAM,WAAW,CAAC,KAAgB,EAAA;AAChC,QAAA,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QAC1E,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnB,OAAO;SACR;AAED,QAAA,MAAM,MAAM,GAAkB;YAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,YAAA,UAAU,EAAE,WAAY;AACxB,YAAA,SAAS,EAAE,SAAU;YACrB,QAAQ,EAAE,QAAQ,IAAI,SAAS;YAC/B,QAAQ,EAAE,QAAQ,IAAI,SAAS;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;AAEF,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,CAAU,CAAC,CAAC;SACzB;KACF;AAED,IAAA,OAAO,CAAC,KAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACpB;AAEO,IAAA,UAAU,CAAC,IAAmB,EAAA;QACpC,QAAQ,IAAI;YACV,KAAqC,gBAAA,uCAAA;AACrC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;YACjB,KAAkC,cAAA,oCAAA;AAClC,YAAA,KAAA,iBAAA;AACE,gBAAA,OAAO,KAAK,CAAC;YACf,KAAoC,gBAAA,sCAAA;AACpC,YAAA,KAAA,mBAAA;AACE,gBAAA,OAAO,OAAO,CAAC;AACjB,YAAA;AACE,gBAAAC,cAAK,CAAC,IAAI,CAAC,IAAI,sDAA+B,CAAC;SAClD;KACF;AAES,IAAA,OAAO,CAAC,IAAmC,EAAA;AACnD,QAAAC,oBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAES,IAAA,MAAM,CAAC,KAAY,EAAA;AAC3B,QAAAA,oBAAW,CAAC,IAAI,CAAC,cAAc,EAAE,+BAA+B,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;IAEO,oBAAoB,GAAA;AAC1B,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC5C;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB;AAGF;;ACtJD;;;;;;;;;;;;;;;AAeG;AA0CI,MAAM,0BAA0B,GAAG,IAAInC,cAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACI,eAAe,eAAe,CACnC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,IAAIc,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnBV,qBAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CAC1D,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAGK,kBAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA2B,0BAAiB,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;IACzD,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACtE,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,EAAA,gBAAA,wCAEZ,QAAQ,EACR,gBAAgB,CACjB,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACI,eAAe,uBAAuB,CAC3C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAGrB,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAIF,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnBV,qBAAY,CAAC,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CACvE,CAAC;KACH;IACDgC,0BAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;IACtE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3E,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAAA,gBAAA,uCAEjB,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACI,eAAe,aAAa,CACjC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAGrB,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9DoB,0BAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;IACtE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAE3E,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAAA,cAAA,qCAEjB,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;AAIG;AACH,MAAM,cAAe,SAAQ,8BAA8B,CAAA;IAOzD,WACE,CAAA,IAAkB,EAClB,MAAqB,EACJ,QAAsB,EACvC,QAAuC,EACvC,IAAmB,EAAA;QAEnB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAJnB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAc;QANjC,IAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QACpC,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;AAUnC,QAAA,IAAI,cAAc,CAAC,kBAAkB,EAAE;AACrC,YAAA,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;SAC5C;AAED,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C;AAED,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACpC,QAAAnC,gBAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AACzD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,MAAM,WAAW,GAAA;QACfiC,oBAAW,CACT,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,QAAA,MAAM,OAAO,GAAGG,yBAAgB,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAC9C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACd,QAAA,OAAO,CACR,CAAC;AACF,QAAA,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,OAAO,CAAC;;;;;;;;AAS1C,QAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;AACnD,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,IAAG;YAClE,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,MAAM,CACTlC,qBAAY,CAAC,IAAI,CAAC,IAAI,EAAwC,yBAAA,6CAAA,CAC/D,CAAC;aACH;AACH,SAAC,CAAC,CAAC;;QAGH,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAED,IAAA,IAAI,OAAO,GAAA;;QACT,OAAO,CAAA,MAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAe,KAAI,IAAI,CAAC;KACjD;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,MAAM,CAACA,qBAAY,CAAC,IAAI,CAAC,IAAI,EAAsC,yBAAA,2CAAA,CAAC,CAAC;KAC3E;IAED,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;SACzB;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAClC;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C;IAEO,oBAAoB,GAAA;QAC1B,MAAM,IAAI,GAAG,MAAW;;YACtB,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;;;;;;gBAMnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;AACnC,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,MAAM,CACTA,qBAAY,CAAC,IAAI,CAAC,IAAI,EAAqC,sBAAA,0CAAA,CAC5D,CAAC;AACJ,iBAAC,iCAAsB,CAAC;gBACxB,OAAO;aACR;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,SAAC,CAAC;AAEF,QAAA,IAAI,EAAE,CAAC;KACR;;AA3GD;AACA;AACe,cAAkB,CAAA,kBAAA,GAA0B,IAA1B;;ACvNnC;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,oBAAoB,GAAG,iBAAiB,CAAC;AAE/C;AACA;AACA,MAAM,kBAAkB,GAGpB,IAAI,GAAG,EAAE,CAAC;AAER,MAAO,cAAe,SAAQ,8BAA8B,CAAA;AAGhE,IAAA,WAAA,CACE,IAAkB,EAClB,QAAuC,EACvC,eAAe,GAAG,KAAK,EAAA;QAEvB,KAAK,CACH,IAAI,EACJ;;;;;AAKC,SAAA,EACD,QAAQ,EACR,SAAS,EACT,eAAe,CAChB,CAAC;QAlBJ,IAAO,CAAA,OAAA,GAAG,IAAI,CAAC;KAmBd;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,YAAY,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,IAAI;AACF,gBAAA,MAAM,kBAAkB,GAAG,MAAM,iCAAiC,CAChE,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,CACV,CAAC;AACF,gBAAA,MAAM,MAAM,GAAG,kBAAkB,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;gBACjE,YAAY,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC9C;YAAC,OAAO,CAAC,EAAE;gBACV,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACxC;AAED,YAAA,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAC;SACxD;;;AAID,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;SACvE;QAED,OAAO,YAAY,EAAE,CAAC;KACvB;IAED,MAAM,WAAW,CAAC,KAAgB,EAAA;AAChC,QAAA,IAAI,KAAK,CAAC,IAAI,KAAA,mBAAA,2CAAyC;AACrD,YAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACjC;AAAM,aAAA,IAAI,KAAK,CAAC,IAAI,KAAA,SAAA,8BAA4B;;AAE/C,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,OAAO;SACR;AAED,QAAA,IAAI,KAAK,CAAC,OAAO,EAAE;AACjB,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,IAAI,EAAE;AACR,gBAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,gBAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACjC;iBAAM;AACL,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;aACpB;SACF;KACF;IAED,MAAM,WAAW,GAAA,GAAoB;AAErC,IAAA,OAAO,MAAW;AACnB,CAAA;AAEM,eAAe,iCAAiC,CACrD,QAAuC,EACvC,IAAkB,EAAA;AAElB,IAAA,MAAM,GAAG,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,MAAM,WAAW,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAClD,IAAI,EAAE,MAAM,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE;AACvC,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,MAAM,kBAAkB,GAAG,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM,CAAC;AACpE,IAAA,MAAM,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,IAAA,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAEM,eAAe,yBAAyB,CAC7C,QAAuC,EACvC,IAAkB,EAAA;AAElB,IAAA,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9E,CAAC;SAEe,sBAAsB,GAAA;IACpC,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAC7B,CAAC;AAEe,SAAA,uBAAuB,CACrC,IAAkB,EAClB,MAAoD,EAAA;IAEpD,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,mBAAmB,CAC1B,QAAuC,EAAA;AAEvC,IAAA,OAAOwB,qBAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,OAAOW,4BAAmB,CACxB,oBAAoB,EACpB,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,IAAI,CAAC,IAAI,CACV,CAAC;AACJ;;AC/JA;;;;;;;;;;;;;;;AAeG;AA4BH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;SACa,kBAAkB,CAChC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAmB,CAAC;AACzE,CAAC;AAEM,eAAe,mBAAmB,CACvC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,IAAIzB,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAGN,kBAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA2B,0BAAiB,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;;;;IAIzD,MAAM,YAAY,CAAC,sBAAsB,CAAC;IAC1C,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AACtE,IAAA,MAAM,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAEhE,OAAO,gBAAgB,CAAC,aAAa,CACnC,YAAY,EACZ,QAAQ,+DAET,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCG;SACa,0BAA0B,CACxC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,2BAA2B,CAChC,IAAI,EACJ,QAAQ,EACR,QAAQ,CACS,CAAC;AACtB,CAAC;AACM,eAAe,2BAA2B,CAC/C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAGrB,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9DoB,0BAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;IACtE,IAAIvB,wBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC;KACH;;;;AAID,IAAA,MAAM,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC;;IAE/C,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3E,MAAM,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AAErE,IAAA,MAAM,OAAO,GAAG,MAAM,sBAAsB,CAAC,YAAY,CAAC,CAAC;AAC3D,IAAA,OAAO,gBAAgB,CAAC,aAAa,CACnC,YAAY,CAAC,IAAI,EACjB,QAAQ,EAAA,mBAAA,0CAER,OAAO,CACR,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;SACa,gBAAgB,CAC9B,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;IAEhC,OAAO,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAmB,CAAC;AACvE,CAAC;AACM,eAAe,iBAAiB,CACrC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAGC,uBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9DoB,0BAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAEC,8BAAqB,CAAC,CAAC;;;;AAItE,IAAA,MAAM,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC;;IAE/C,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3E,MAAMnB,4BAAmB,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;IACpE,MAAM,yBAAyB,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;AAErE,IAAA,MAAM,OAAO,GAAG,MAAM,sBAAsB,CAAC,YAAY,CAAC,CAAC;AAC3D,IAAA,OAAO,gBAAgB,CAAC,aAAa,CACnC,YAAY,CAAC,IAAI,EACjB,QAAQ,EAAA,iBAAA,wCAER,OAAO,CACR,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;AACI,eAAe,iBAAiB,CACrC,IAAU,EACV,QAAgC,EAAA;AAEhC,IAAA,MAAMT,kBAAS,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC;IAC7C,OAAO,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACnD,CAAC;AAEM,eAAe,kBAAkB,CACtC,IAAU,EACV,cAAsC,EACtC,eAAe,GAAG,KAAK,EAAA;AAEvB,IAAA,IAAIK,wBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnBC,wDAA+C,CAAC,IAAI,CAAC,CACtD,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAGN,kBAAS,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;IACpE,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;AAC3E,IAAA,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;AAEtC,IAAA,IAAI,MAAM,IAAI,CAAC,eAAe,EAAE;AAC9B,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACpC,MAAM,YAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAoB,CAAC,CAAC;QACtE,MAAM,YAAY,CAAC,gBAAgB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;KAC3D;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,eAAe,sBAAsB,CAAC,IAAkB,EAAA;IACtD,MAAM,OAAO,GAAG6B,yBAAgB,CAAC,CAAA,EAAG,IAAI,CAAC,GAAG,CAAK,GAAA,CAAA,CAAC,CAAC;AACnD,IAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;IAChC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAA,OAAO,OAAO,CAAC;AACjB;;AClVA;;;;;;;;;;;;;;;AAeG;AAYH;AACA;AACA,MAAM,mCAAmC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAE9C,gBAAgB,CAAA;AAO3B,IAAA,WAAA,CAA6B,IAAkB,EAAA;QAAlB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;AAN9B,QAAA,IAAA,CAAA,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;AACzC,QAAA,IAAA,CAAA,SAAS,GAA2B,IAAI,GAAG,EAAE,CAAC;QACrD,IAAmB,CAAA,mBAAA,GAAqB,IAAI,CAAC;QAC7C,IAA2B,CAAA,2BAAA,GAAG,KAAK,CAAC;AACtC,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAEO;AAEnD,IAAA,gBAAgB,CAAC,iBAAoC,EAAA;AACnD,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEtC,IACE,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,EACpE;YACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAChD,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;KACF;AAED,IAAA,kBAAkB,CAAC,iBAAoC,EAAA;AACrD,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC1C;AAED,IAAA,OAAO,CAAC,KAAgB,EAAA;;AAEtB,QAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AACnC,YAAA,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;YAChC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBAC5C,OAAO,GAAG,IAAI,CAAC;AACf,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACrC,gBAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;aAC9B;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,2BAA2B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;;;AAG/D,YAAA,OAAO,OAAO,CAAC;SAChB;AAED,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;;QAGxC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACjC,OAAO,GAAG,IAAI,CAAC;SAChB;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;IAEO,cAAc,CAAC,KAAgB,EAAE,QAA2B,EAAA;;QAClE,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;AAC9C,YAAA,MAAM,IAAI,GACR,CAAC,CAAA,EAAA,GAAA,KAAK,CAAC,KAAK,CAAC,IAAI,0CAAE,KAAK,CAAC,OAAO,CAAA,CAAE,CAAC,CAAmB;oEACzB;AAC/B,YAAA,QAAQ,CAAC,OAAO,CAAClC,qBAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SACjD;aAAM;AACL,YAAA,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC7B;KACF;IAEO,kBAAkB,CACxB,KAAgB,EAChB,QAA2B,EAAA;AAE3B,QAAA,MAAM,cAAc,GAClB,QAAQ,CAAC,OAAO,KAAK,IAAI;AACzB,aAAC,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC;KAC/D;AAEO,IAAA,mBAAmB,CAAC,KAAgB,EAAA;AAC1C,QAAA,IACE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,sBAAsB;AACxC,YAAA,mCAAmC,EACnC;AACA,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;KAClD;AAEO,IAAA,gBAAgB,CAAC,KAAgB,EAAA;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;KAC1C;AACF,CAAA;AAED,SAAS,QAAQ,CAAC,CAAY,EAAA;AAC5B,IAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAa,EAAA;IACrD,QACE,IAAI,KAA0B,SAAA;AAC9B,QAAA,CAAA,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,IAAI,MAAK,CAAQ,KAAA,EAAA,eAAA,mCAA6B,CAAA,EACrD;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,KAAgB,EAAA;AACvC,IAAA,QAAQ,KAAK,CAAC,IAAI;QAChB,KAAwC,mBAAA,0CAAA;QACxC,KAAqC,iBAAA,uCAAA;AACrC,QAAA,KAAA,mBAAA;AACE,YAAA,OAAO,IAAI,CAAC;AACd,QAAA,KAAA,SAAA;AACE,YAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpC,QAAA;AACE,YAAA,OAAO,KAAK,CAAC;KAChB;AACH;;ACrJA;;;;;;;;;;;;;;;AAeG;AAcI,eAAe,iBAAiB,CACrC,IAAU,EACV,UAAmC,EAAE,EAAA;AAErC,IAAA,OAAOoC,2BAAkB,CACvB,IAAI,EAGJ,KAAA,uBAAA,cAAA,oCAAA,OAAO,CACR,CAAC;AACJ;;ACvCA;;;;;;;;;;;;;;;AAeG;AAQH,MAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAChE,MAAM,UAAU,GAAG,SAAS,CAAC;AAEtB,eAAeC,iBAAe,CAAC,IAAkB,EAAA;;AAEtD,IAAA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QACxB,OAAO;KACR;IAED,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAE5D,IAAA,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE;AACtC,QAAA,IAAI;AACF,YAAA,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;gBACvB,OAAO;aACR;SACF;AAAC,QAAA,OAAA,EAAA,EAAM;;SAEP;KACF;;IAGDP,cAAK,CAAC,IAAI,EAAA,qBAAA,oCAA+B,CAAC;AAC5C,CAAC;AAED,SAAS,WAAW,CAAC,QAAgB,EAAA;AACnC,IAAA,MAAM,UAAU,GAAGQ,uBAAc,EAAE,CAAC;IACpC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;AACnD,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE;AAC9C,QAAA,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;;YAE5C,QACE,QAAQ,KAAK,mBAAmB;AAChC,gBAAA,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBACzC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC/C;SACH;QAED,OAAO,QAAQ,KAAK,mBAAmB,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;KACxE;IAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;QAGnC,OAAO,QAAQ,KAAK,QAAQ,CAAC;KAC9B;;IAGD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;AAG5D,IAAA,MAAM,EAAE,GAAG,IAAI,MAAM,CACnB,SAAS,GAAG,oBAAoB,GAAG,GAAG,GAAG,oBAAoB,GAAG,IAAI,EACpE,GAAG,CACJ,CAAC;AACF,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B;;ACrFA;;;;;;;;;;;;;;;AAeG;AASH,MAAM,eAAe,GAAG,IAAI1C,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEhD;;;AAGG;AACH,SAAS,wBAAwB,GAAA;;;;AAI/B,IAAA,MAAM,MAAM,GAAGC,gBAAO,EAAE,CAAC,MAAM,CAAC;;IAEhC,IAAI,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,CAAC,EAAE;;AAEb,QAAA,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;;AAExC,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzC,YAAA,IAAI,MAAM,CAAC,EAAE,EAAE;AACb,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEzC,oBAAA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;iBACrB;aACF;SACF;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,IAAkB,EAAA;IAClC,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,KAAI;;;AAE3D,QAAA,SAAS,cAAc,GAAA;;;AAGrB,YAAA,wBAAwB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,QAAQ,EAAE,MAAK;oBACb,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;iBACpC;gBACD,SAAS,EAAE,MAAK;;;;;;;AAOd,oBAAA,wBAAwB,EAAE,CAAC;AAC3B,oBAAA,MAAM,CAACG,qBAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,EAAE;AAC/B,aAAA,CAAC,CAAC;SACJ;QAED,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAAH,gBAAO,EAAE,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;;YAEnC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;SACpC;AAAM,aAAA,IAAI,CAAC,EAAC,CAAA,EAAA,GAAAA,gBAAO,EAAE,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAA,EAAE;;AAEjC,YAAA,cAAc,EAAE,CAAC;SAClB;aAAM;;;;;;YAML,MAAM,MAAM,GAAG0C,8BAAwB,CAAC,WAAW,CAAC,CAAC;;AAErD,YAAA1C,gBAAO,EAAE,CAAC,MAAM,CAAC,GAAG,MAAK;;AAEvB,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;AACf,oBAAA,cAAc,EAAE,CAAC;iBAClB;qBAAM;;AAEL,oBAAA,MAAM,CAACG,qBAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACH,aAAC,CAAC;;AAEF,YAAA,OAAOwC,gBACG,CAAC,GAAGC,uBAAiB,EAAE,CAAA,QAAA,EAAW,MAAM,CAAA,CAAE,CAAC;iBAClD,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1B;AACH,KAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAG;;QAEf,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,MAAM,KAAK,CAAC;AACd,KAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,gBAAgB,GAAyC,IAAI,CAAC;AAC5D,SAAU,SAAS,CAAC,IAAkB,EAAA;AAC1C,IAAA,gBAAgB,GAAG,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtD,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACxHA;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,YAAY,GAAG,IAAI7C,cAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC;AACrC,MAAM,oBAAoB,GAAG,sBAAsB,CAAC;AAEpD,MAAM,iBAAiB,GAAG;AACxB,IAAA,KAAK,EAAE;AACL,QAAA,QAAQ,EAAE,UAAU;AACpB,QAAA,GAAG,EAAE,QAAQ;AACb,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,MAAM,EAAE,KAAK;AACd,KAAA;AACD,IAAA,aAAa,EAAE,MAAM;AACrB,IAAA,QAAQ,EAAE,IAAI;CACf,CAAC;AAEF;AACA;AACA,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;IAC/B,CAAyB,gCAAA,+BAAA,GAAG,CAAC;AAC7B,IAAA,CAAC,gDAAgD,EAAE,GAAG,CAAC;AACvD,IAAA,CAAC,6CAA6C,EAAE,GAAG,CAAC;AACrD,CAAA,CAAC,CAAC;AAEH,SAAS,YAAY,CAAC,IAAkB,EAAA;AACtC,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAAE,gBAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,wEAAoC,CAAC;AACpE,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ;AACzB,UAAE4C,qBAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC;UAC1C,CAAW,QAAA,EAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,CAAA,EAAI,WAAW,CAAA,CAAE,CAAC;AAEvD,IAAA,MAAM,MAAM,GAA2B;QACrC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,IAAI,CAAC,IAAI;AAClB,QAAA,CAAC,EAAEC,eAAW;KACf,CAAC;AACF,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACtD,IAAI,GAAG,EAAE;AACP,QAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;KAClB;AACD,IAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACzC,IAAA,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClC;AACD,IAAA,OAAO,CAAG,EAAA,GAAG,CAAI,CAAA,EAAAzC,gBAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;AAClD,CAAC;AAEM,eAAe,WAAW,CAC/B,IAAkB,EAAA;IAElB,MAAM,OAAO,GAAG,MAAM0C,SAAoB,CAAC,IAAI,CAAC,CAAC;AACjD,IAAA,MAAM,IAAI,GAAG/C,gBAAO,EAAE,CAAC,IAAI,CAAC;AAC5B,IAAAC,gBAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;IAClD,OAAO,OAAO,CAAC,IAAI,CACjB;QACE,KAAK,EAAE,QAAQ,CAAC,IAAI;AACpB,QAAA,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC;AACvB,QAAA,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B;AAC/D,QAAA,UAAU,EAAE,iBAAiB;AAC7B,QAAA,SAAS,EAAE,IAAI;AAChB,KAAA,EACD,CAAC,MAA2B,KAC1B,IAAI,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,KAAI;QACpC,MAAM,MAAM,CAAC,OAAO,CAAC;;AAEnB,YAAA,cAAc,EAAE,KAAK;AACtB,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,YAAY,GAAGE,qBAAY,CAC/B,IAAI,sEAEL,CAAC;;;QAGF,MAAM,iBAAiB,GAAGH,gBAAO,EAAE,CAAC,UAAU,CAAC,MAAK;YAClD,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,SAAC,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;AAEvB,QAAA,SAAS,oBAAoB,GAAA;AAC3B,YAAAA,gBAAO,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;YAC1C,OAAO,CAAC,MAAM,CAAC,CAAC;SACjB;;;QAGD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAK;YAChE,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ,CAAC,CACL,CAAC;AACJ;;ACrHA;;;;;;;;;;;;;;;AAeG;AAaH,MAAM,kBAAkB,GAAG;AACzB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,MAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;MAEhC,SAAS,CAAA;AAGpB,IAAA,WAAA,CAAqB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAF1C,IAAe,CAAA,eAAA,GAAkB,IAAI,CAAC;KAEQ;IAE9C,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;aACrB;AAAC,YAAA,OAAO,CAAC,EAAE,GAAE;SACf;KACF;AACF,CAAA;AAEe,SAAA,KAAK,CACnB,IAAkB,EAClB,GAAY,EACZ,IAAa,EACb,KAAK,GAAG,aAAa,EACrB,MAAM,GAAG,cAAc,EAAA;IAEvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC7E,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5E,IAAI,MAAM,GAAG,EAAE,CAAC;AAEhB,IAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,kBAAkB,KACrB,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EACvB,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,EACzB,GAAG;AACH,QAAA,IAAI,GACL,CAAC;;;AAIF,IAAA,MAAM,EAAE,GAAGgD,UAAK,EAAE,CAAC,WAAW,EAAE,CAAC;IAEjC,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAGC,qBAAY,CAAC,EAAE,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;KACjD;AAED,IAAA,IAAIC,mBAAU,CAAC,EAAE,CAAC,EAAE;;AAElB,QAAA,GAAG,GAAG,GAAG,IAAI,iBAAiB,CAAC;;;AAG/B,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;KAC5B;AAED,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAClD,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,EACnD,EAAE,CACH,CAAC;IAEF,IAAIC,yBAAgB,CAAC,EAAE,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;AAC9C,QAAA,kBAAkB,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AACtC,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5B;;;AAID,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAC7D,IAAAlD,gBAAO,CAAC,MAAM,EAAE,IAAI,oDAA8B,CAAC;;AAGnD,IAAA,IAAI;QACF,MAAM,CAAC,KAAK,EAAE,CAAC;KAChB;AAAC,IAAA,OAAO,CAAC,EAAE,GAAE;AAEd,IAAA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,MAAc,EAAA;IACrD,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACvC,IAAA,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AACd,IAAA,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD,IAAA,KAAK,CAAC,cAAc,CAClB,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,CAAC,EACD,IAAI,CACL,CAAC;AACF,IAAA,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B;;ACxIA;;;;;;;;;;;;;;;AAeG;AAaH;;;;AAIG;AACH,MAAM,WAAW,GAAG,iBAAiB,CAAC;AAEtC;;;;AAIG;AACH,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;AAErD;;;;AAIG;AACH,MAAM,8BAA8B,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAgB1D,eAAe,eAAe,CACnC,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,WAAoB,EACpB,OAAgB,EAChB,gBAAyC,EAAA;IAEzCA,gBAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,EAAA,6BAAA,yCAAoC,CAAC;IACzEA,gBAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAA,iBAAA,qCAAgC,CAAC;AAEjE,IAAA,MAAM,MAAM,GAAiB;AAC3B,QAAA,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;QAC1B,OAAO,EAAE,IAAI,CAAC,IAAI;QAClB,QAAQ;QACR,WAAW;AACX,QAAA,CAAC,EAAE6C,eAAW;QACd,OAAO;KACR,CAAC;AAEF,IAAA,IAAI,QAAQ,YAAYV,8BAAqB,EAAE;AAC7C,QAAA,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/C,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,IAAI,CAACgB,YAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,EAAE;AAC5C,YAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC,CAAC;SAC1E;;AAGD,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE;AACjE,YAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACrB;KACF;AAED,IAAA,IAAI,QAAQ,YAAYC,0BAAiB,EAAE;AACzC,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;AAClE,QAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;KACF;AAED,IAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,QAAA,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;KAC5B;;;IAKD,MAAM,UAAU,GAAG,MAAyC,CAAC;IAC7D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,QAAA,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AACjC,YAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;SACxB;KACF;;AAGD,IAAA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACrD,MAAM,qBAAqB,GAAG,aAAa;UACvC,IAAI,8BAA8B,CAAA,CAAA,EAAI,kBAAkB,CAAC,aAAa,CAAC,CAAE,CAAA;UACzE,EAAE,CAAC;;AAGP,IAAA,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA,CAAA,EAAIhD,gBAAW,CAAC,UAAU,CAAC,CAAC,KAAK,CAC7D,CAAC,CACF,CAAG,EAAA,qBAAqB,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,MAAM,EAAgB,EAAA;AAC9C,IAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACpB,QAAA,OAAO,WAAW,MAAM,CAAC,UAAU,CAAI,CAAA,EAAA,WAAW,EAAE,CAAC;KACtD;AAED,IAAA,OAAOwC,qBAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;AACpD;;ACvIA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;AACH,MAAM,uBAAuB,GAAG,mBAAmB,CAAC;AAWpD,MAAM,4BAA4B,CAAA;AAAlC,IAAA,WAAA,GAAA;QACmB,IAAa,CAAA,aAAA,GAAqC,EAAE,CAAC;QACrD,IAAO,CAAA,OAAA,GAAwC,EAAE,CAAC;QAClD,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAErE,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;QAyH1D,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QAEzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KACnD;;;IAxHC,MAAM,UAAU,CACd,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;AAEhB,QAAAX,oBAAW,CACT,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EACxC,8CAA8C,CAC/C,CAAC;AAEF,QAAA,MAAM,GAAG,GAAG,MAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACRO,uBAAc,EAAE,EAChB,OAAO,CACR,CAAC;QACF,OAAO,KAAK,CAAC,IAAI,EAAE,GAAG,EAAEJ,yBAAgB,EAAE,CAAC,CAAC;KAC7C;IAED,MAAM,aAAa,CACjB,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;AAEhB,QAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACnC,QAAA,MAAM,GAAG,GAAG,MAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACRI,uBAAc,EAAE,EAChB,OAAO,CACR,CAAC;QACFa,2BAAkB,CAAC,GAAG,CAAC,CAAC;QACxB,OAAO,IAAI,OAAO,CAAC,MAAO,GAAC,CAAC,CAAC;KAC9B;AAED,IAAA,WAAW,CAAC,IAAkB,EAAA;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;AAC3B,YAAA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACjC;iBAAM;AACL,gBAAApB,oBAAW,CAAC,OAAO,EAAE,0CAA0C,CAAC,CAAC;AACjE,gBAAA,OAAO,OAAO,CAAC;aAChB;SACF;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC;;;AAItC,QAAA,OAAO,CAAC,KAAK,CAAC,MAAK;AACjB,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC;KAChB;IAEO,MAAM,iBAAiB,CAAC,IAAkB,EAAA;AAChD,QAAA,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC;AACvC,QAAA,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,CAAC,QAAQ,CACb,WAAW,EACX,CAAC,WAAiC,KAAI;YACpCjC,gBAAO,CAAC,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,SAAS,EAAE,IAAI,EAAA,oBAAA,wCAAmC,CAAC;;YAGxE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,EAAE,MAAM,EAAE,OAAO,GAAmB,KAAA,yBAAmB,OAAA,0BAAE,CAAC;AACnE,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;AAEF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,QAAA,OAAO,OAAO,CAAC;KAChB;IAED,4BAA4B,CAC1B,IAAkB,EAClB,EAAmC,EAAA;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACzC,QAAA,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACjC,MAAM,IAAG;;AACP,YAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAG,CAAC,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,uBAAuB,CAAC,CAAC;AAC3D,YAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,gBAAA,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;aACnB;YAEDgC,cAAK,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC5C,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;KACH;AAED,IAAA,iBAAiB,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAGO,iBAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C;AAED,IAAA,IAAI,sBAAsB,GAAA;;QAExB,OAAO5C,yBAAgB,EAAE,IAAI2D,kBAAS,EAAE,IAAIC,eAAM,EAAE,CAAC;KACtD;AAKF,CAAA;AAED;;;;;;;;AAQG;AACI,MAAM,4BAA4B,GACvC;;ACxKF;;;;AAIG;AACG,MAAO,6BACX,SAAQC,iCAAwB,CAAA;AAGhC,IAAA,WAAA,CAAqC,UAA+B,EAAA;AAClE,QAAA,KAAK,8BAAgB,CAAC;QADa,IAAU,CAAA,UAAA,GAAV,UAAU,CAAqB;KAEnE;;IAGD,OAAO,eAAe,CACpB,UAA+B,EAAA;AAE/B,QAAA,OAAO,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;KACtD;;AAGD,IAAA,eAAe,CACb,IAAkB,EAClB,OAAe,EACf,WAA2B,EAAA;QAE3B,OAAOC,+BAAsB,CAAC,IAAI,EAAE;YAClC,OAAO;YACP,WAAW;AACX,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ;;IAGD,eAAe,CACb,IAAkB,EAClB,oBAA4B,EAAA;QAE5B,OAAOC,+BAAsB,CAAC,IAAI,EAAE;YAClC,oBAAoB;AACpB,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ;AACF,CAAA;AAED;;;;AAIG;MACU,yBAAyB,CAAA;AACpC,IAAA,WAAA,GAAA,GAAwB;AAExB;;;;;;;;;AASG;IACH,OAAO,SAAS,CAAC,UAA+B,EAAA;AAC9C,QAAA,OAAO,6BAA6B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KAClE;;AAED;;AAEG;AACI,yBAAS,CAAA,SAAA,GAAG,OAAO;;ACpG5B;;;;;;;;;;;;;;;AAeG;AAsBH,MAAM,wBAAwB,GAAG,CAAC,GAAG,EAAE,CAAC;AACxC,MAAM,iBAAiB,GACrBC,2BAAsB,CAAC,mBAAmB,CAAC,IAAI,wBAAwB,CAAC;AAE1E,IAAI,iBAAiB,GAA8B,IAAI,CAAC;AAExD,MAAM,iBAAiB,GAAG,CAAC,GAAW,KAAK,OAAO,IAAiB,KAAI;IACrE,MAAM,aAAa,GAAG,IAAI,KAAK,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC9D,MAAM,UAAU,GACd,aAAa;AACb,QAAA,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,IAAK,CAAC;AAC1E,IAAA,IAAI,UAAU,IAAI,UAAU,GAAG,iBAAiB,EAAE;QAChD,OAAO;KACR;;IAED,MAAM,OAAO,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,KAAK,CAAC;AACrC,IAAA,IAAI,iBAAiB,KAAK,OAAO,EAAE;QACjC,OAAO;KACR;IACD,iBAAiB,GAAG,OAAO,CAAC;IAC5B,MAAM,KAAK,CAAC,GAAG,EAAE;QACf,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ;AACnC,QAAA,OAAO,EAAE,OAAO;AACd,cAAE;gBACE,eAAe,EAAE,CAAU,OAAA,EAAA,OAAO,CAAE,CAAA;AACrC,aAAA;AACH,cAAE,EAAE;AACP,KAAA,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;;;;;AAOG;AACa,SAAA,OAAO,CAACC,KAAA,GAAmBC,UAAM,EAAE,EAAA;IACjD,MAAM,QAAQ,GAAGC,gBAAY,CAACF,KAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;KAChC;AAED,IAAA,MAAM,IAAI,GAAGG,uBAAc,CAACH,KAAG,EAAE;AAC/B,QAAA,qBAAqB,EAAE,4BAA4B;AACnD,QAAA,WAAW,EAAE;YACXI,kCAAyB;YACzB,uBAAuB;YACvB,yBAAyB;AAC1B,SAAA;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,iBAAiB,GAAGL,2BAAsB,CAAC,kBAAkB,CAAC,CAAC;;AAErE,IAAA,IACE,iBAAiB;QACjB,OAAO,eAAe,KAAK,SAAS;AACpC,QAAA,eAAe,EACf;;QAEA,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrE,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;YAC/C,MAAM,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClE,YAAAM,+BAAsB,CAAC,IAAI,EAAE,UAAU,EAAE,MACvC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;AACF,YAAAC,yBAAgB,CAAC,IAAI,EAAE,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;SAClD;KACF;AAED,IAAA,MAAM,gBAAgB,GAAGC,2BAAsB,CAAC,MAAM,CAAC,CAAC;IACxD,IAAI,gBAAgB,EAAE;AACpB,QAAAC,4BAAmB,CAAC,IAAI,EAAE,UAAU,gBAAgB,CAAA,CAAE,CAAC,CAAC;KACzD;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,GAAA;;AAC7B,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,QAAQ,CAAC;AAChE,CAAC;AAEDC,+BAAsB,CAAC;AACrB,IAAA,MAAM,CAAC,GAAW,EAAA;;QAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5C,YAAA,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5B,YAAA,EAAE,CAAC,MAAM,GAAG,OAAO,CAAC;AACpB,YAAA,EAAE,CAAC,OAAO,GAAG,CAAC,IAAG;AACf,gBAAA,MAAM,KAAK,GAAGnE,qBAAY,CAAA,gBAAA,oCAA8B,CAAC;AACzD,gBAAA,KAAK,CAAC,UAAU,GAAG,CAAuC,CAAC;gBAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;AAChB,aAAC,CAAC;AACF,YAAA,EAAE,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC5B,YAAA,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;AACrB,YAAA,sBAAsB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,UAAU,EAAE,mCAAmC;AAC/C,IAAA,iBAAiB,EAAE,yCAAyC;AAC5D,IAAA,yBAAyB,EACvB,wDAAwD;AAC3D,CAAA,CAAC,CAAC;AAEHoE,qBAAY,wCAAwB;;ACjJpC;;;;;;;;;;;;;;;AAeG;SAoCa,cAAc,GAAA;AAC5B,IAAA,OAAO,MAAkC,CAAC;AAC5C;;ACrDA;;;;;;;;;;;;;;;AAeG;AAoBH;;;AAGG;AACH,MAAM,mBAAmB,GAAG,IAAI,CAAC;AAEjC;;AAEG;AACI,eAAe,mBAAmB,CACvC,IAAkB,EAClB,KAAgB,EAChB,QAAsB,EAAA;;;AAGtB,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;AACvC,IAAArC,oBAAW,CAAC,KAAK,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAC;IACvE,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAE3D,MAAM,gBAAgB,GAA2B,EAAE,CAAC;IACpD,IAAIsB,eAAM,EAAE,EAAE;;AAEZ,QAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;SAAM,IAAIgB,mBAAU,EAAE,EAAE;;AAEvB,QAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KACjD;SAAM;QACLvC,cAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;;AAGD,IAAA,IAAI,SAAS,CAAC,WAAW,EAAE;AACzB,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;KAC5D;;AAGD,IAAA,gBAAgB,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;IAC9C,OAAO,eAAe,CACpB,IAAI,EACJ,QAAQ,EACR,KAAK,CAAC,IAAI,EACV,SAAS,EACT,CAAA,EAAA,GAAA,KAAK,CAAC,OAAO,mCAAI,SAAS,EAC1B,gBAAgB,CACjB,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,eAAe,eAAe,CAAC,IAAkB,EAAA;AACtD,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;IACvC,MAAM,OAAO,GAA4B,EAAE,CAAC;IAC5C,IAAIuB,eAAM,EAAE,EAAE;AACZ,QAAA,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;KAC7C;SAAM,IAAIgB,mBAAU,EAAE,EAAE;AACvB,QAAA,OAAO,CAAC,kBAAkB,GAAG,SAAS,CAAC,WAAW,CAAC;KACpD;SAAM;QACLvC,cAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;;AAGD,IAAA,MAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzC,CAAC;AAEK,SAAU,gBAAgB,CAC9B,UAAkB,EAAA;;AAGlB,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;AAErC,IAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;QAC3B,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,qBAAqB,IAAG;YAC7D,IAAI,MAAM,GAA2B,IAAI,CAAC;YAC1C,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aAChD;iBAAM;;gBAEL,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAChC,UAAU,EACVwC,mBAAU,EAAE,GAAG,QAAQ,GAAG,SAAS,EACnC,cAAc,CACf,CAAC;aACH;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAQD;;;;;AAKG;AACI,eAAe,iBAAiB,CACrC,IAAkB,EAClB,aAAuC,EACvC,MAA8B,EAAA;;AAG9B,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,EAAE,CAAC;AAErC,IAAA,IAAI,OAAO,GAAG,MAAW,GAAG,CAAC;AAC7B,IAAA,IAAI;QACF,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;YAC1C,IAAI,YAAY,GAAkB,IAAI,CAAC;;AAGvC,YAAA,SAAS,aAAa,GAAA;;;;AAGpB,gBAAA,OAAO,EAAE,CAAC;gBACV,MAAM,eAAe,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,OAAO,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC;AAC1D,gBAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AACzC,oBAAA,eAAe,EAAE,CAAC;iBACnB;;;AAGD,gBAAA,IAAI,QAAO,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,KAAK,CAAA,KAAK,UAAU,EAAE;oBACvC,MAAM,CAAC,KAAK,EAAE,CAAC;iBAChB;aACF;AAED,YAAA,SAAS,OAAO,GAAA;gBACd,IAAI,YAAY,EAAE;;oBAEhB,OAAO;iBACR;AAED,gBAAA,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;;AAEpC,oBAAA,MAAM,CAACtE,qBAAY,CAAC,IAAI,EAAA,4BAAA,gDAA2C,CAAC,CAAC;iBACtE,EAAE,mBAAmB,CAAC,CAAC;aACzB;AAED,YAAA,SAAS,iBAAiB,GAAA;gBACxB,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,eAAe,MAAK,SAAS,EAAE;AAC3C,oBAAA,OAAO,EAAE,CAAC;iBACX;aACF;;;AAID,YAAA,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;;YAGhD,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACpD,IAAIqE,mBAAU,EAAE,EAAE;gBAChB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;aACzE;;YAGD,OAAO,GAAG,MAAK;AACb,gBAAA,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;gBACnD,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBACvD,QAAQ,CAAC,mBAAmB,CAC1B,kBAAkB,EAClB,iBAAiB,EACjB,KAAK,CACN,CAAC;gBACF,IAAI,YAAY,EAAE;AAChB,oBAAA,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;iBACnC;AACH,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;YAAS;AACR,QAAA,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;;AAC3D,IAAA,MAAM,GAAG,GAAG,cAAc,EAAE,CAAC;;;;;;AAM7B,IAAAvE,gBAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,cAAc,0CAAE,SAAS,CAAA,KAAK,UAAU,EACpD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,oCAAoC;AACpD,KAAA,CACF,CAAC;;AAGF,IAAAA,gBAAO,CACL,QAAO,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,SAAS,0CAAE,WAAW,CAAA,KAAK,WAAW,EAClD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,0BAA0B;AAC1C,KAAA,CACF,CAAC;;IAGFA,gBAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,CAAA,KAAK,UAAU,EAChE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;IACFA,gBAAO,CACL,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAH,GAAG,CAAE,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,CAAA,KAAK,UAAU,EACpE,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,2BAA2B;AAC3C,KAAA,CACF,CAAC;;IAGFA,gBAAO,CACL,QAAO,CAAA,EAAA,GAAA,MAAA,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAA,KAAK,UAAU,EACtD,IAAI,EAEJ,+BAAA,oDAAA;AACE,QAAA,aAAa,EAAE,6BAA6B;AAC7C,KAAA,CACF,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACH,eAAe,aAAa,CAAC,SAAiB,EAAA;AAC5C,IAAA,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;;;;;AAM7C,IAAA,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACzD,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAA;;;IAGtCiC,oBAAW,CACT,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,IAAA,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACtC,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACtC;IAED,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAC7B;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AClTA;;;;;;;;;;;;;;;AAeG;AAgBH,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAE7B;AACM,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;AAA7D,IAAA,WAAA,GAAA;;AACmB,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,GAAG,EAA0B,CAAC;AAE9D,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,OAAO,CAAO,OAAO,IAAG;AAChD,YAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;AACpC,SAAC,CAAC,CAAC;KA2BJ;AAzBC,IAAA,kBAAkB,CAAC,EAA0B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;KAC/B;AAED,IAAA,qBAAqB,CAAC,EAA0B,EAAA;AAC9C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KAClC;;;IAID,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;KAC1C;;AAGD,IAAA,OAAO,CAAC,KAAgB,EAAA;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED,IAAA,MAAM,WAAW,GAAA;QACf,MAAM,IAAI,CAAC,WAAW,CAAC;KACxB;AACF,CAAA;AAED;;AAEG;AACG,SAAU,iBAAiB,CAC/B,IAAkB,EAClB,IAAmB,EACnB,UAAyB,IAAI,EAAA;IAE7B,OAAO;QACL,IAAI;QACJ,OAAO;AACP,QAAA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,iBAAiB,EAAE;AAC9B,QAAA,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,QAAA,KAAK,EAAE/B,qBAAY,CAAC,IAAI,EAA8B,eAAA,mCAAA;KACvD,CAAC;AACJ,CAAC;AAEe,SAAA,iBAAiB,CAC/B,IAAkB,EAClB,KAAgB,EAAA;AAEhB,IAAA,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAgC,CAAC,CAAC;AAChF,CAAC;AAEM,eAAe,kBAAkB,CACtC,IAAkB,EAAA;AAElB,IAAA,MAAM,KAAK,IAAI,MAAM,OAAO,EAAE,CAAC,IAAI,CACjC,cAAc,CAAC,IAAI,CAAC,CACrB,CAAqB,CAAC;IACvB,IAAI,KAAK,EAAE;QACT,MAAM,OAAO,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;KAC/C;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEe,SAAA,uBAAuB,CACrC,YAAuB,EACvB,GAAW,EAAA;;;AAGX,IAAA,MAAM,WAAW,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC;;;;;;AAMlD,IAAA,IAAI,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;;;;AAI7C,QAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;;AAEhD,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC;cACvC,eAAe,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC;cAC5D,IAAI,CAAC;QACT,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAG,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,OAAO,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,CAAC;AACxD,QAAA,MAAM,KAAK,GAAG,IAAI,GAAGA,qBAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC/C,IAAI,KAAK,EAAE;YACT,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,KAAK;AACL,gBAAA,WAAW,EAAE,IAAI;AACjB,gBAAA,SAAS,EAAE,IAAI;AACf,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;SACH;aAAM;YACL,OAAO;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,OAAO,EAAE,YAAY,CAAC,OAAO;gBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;AACjC,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,QAAQ,EAAE,IAAI;aACf,CAAC;SACH;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,MAAM,YAAY,GAChB,gEAAgE,CAAC;AACnE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5D,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KACtC;AACD,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AAED,SAAS,OAAO,GAAA;AACd,IAAA,OAAOwB,qBAAY,CAAC,uBAAuB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAOW,4BAAmB,CAAA,WAAA,2BAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAA;AACnC,IAAA,IAAI;AACF,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KACzB;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAED;AACM,SAAU,wBAAwB,CAAC,GAAW,EAAA;AAClD,IAAA,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,SAAS,CAAC;;IAE7E,MAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;;AAEzD,IAAA,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC;AACxC,UAAE,kBAAkB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;UAC1C,SAAS,CAAC;IACd,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IACnE,OAAO,iBAAiB,IAAI,WAAW,IAAI,cAAc,IAAI,IAAI,IAAI,GAAG,CAAC;AAC3E,CAAC;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAAC,GAAuB,EAAA;AAClD,IAAA,IAAI,EAAC,GAAG,KAAA,IAAA,IAAH,GAAG,KAAH,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,GAAG,CAAE,QAAQ,CAAC,GAAG,CAAC,CAAA,EAAE;AACvB,QAAA,OAAO,EAAE,CAAC;KACX;AAED,IAAA,MAAM,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpC,OAAOoC,sBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2B,CAAC;AACrE;;AC7MA;;;;;;;;;;;;;;;AAeG;AAmCH;;;AAGG;AACH,MAAM,wBAAwB,GAAG,GAAG,CAAC;AAErC,MAAM,4BAA4B,CAAA;AAAlC,IAAA,WAAA,GAAA;QACW,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;AACjD,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,CAAC;AACtB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAmC,CAAC;QAC3D,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAE9E,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QACzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KAwHnD;IAtHC,MAAM,WAAW,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,OAAO,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,YAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SAC7C;AACD,QAAA,OAAO,OAAO,CAAC;KAChB;AAED,IAAA,UAAU,CAAC,IAAkB,EAAA;QAC3BzC,cAAK,CAAC,IAAI,EAAA,6CAAA,6CAAwC,CAAC;KACpD;IAED,MAAM,aAAa,CACjB,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;QAEhB,0BAA0B,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7C,QAAA,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;;;;QAK5B,OAAO,CAAC,aAAa,EAAE,CAAC;AACxB,QAAA,sBAAsB,EAAE,CAAC;AAEzB,QAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,QAAA,MAAM,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7D,QAAA,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KACjD;IAED,4BAA4B,CAC1B,KAAmB,EACnB,GAAkC,EAAA;AAElC,QAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C;AAED,IAAA,iBAAiB,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C;IAEO,uBAAuB,CAC7B,IAAkB,EAClB,OAAyB,EAAA;;QAGzB,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,cAAc,EAAE,CAAC;AAEtE,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,YAAW;;;AAG3C,YAAA,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/B,YAAA,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;SACpC,EAAE,wBAAwB,CAAC,CAAC;AAE7B,QAAA,MAAM,gBAAgB,GAAG,OACvB,SAAwC,KACvB;;YAEjB,YAAY,CAAC,cAAc,CAAC,CAAC;AAE7B,YAAA,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,UAAU,GAAqB,IAAI,CAAC;AACxC,YAAA,IAAI,YAAY,KAAI,SAAS,KAAA,IAAA,IAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAG,KAAK,CAAC,CAAA,EAAE;gBACtC,UAAU,GAAG,uBAAuB,CAAC,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aACtE;;YAGD,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,eAAe,EAAE,CAAC,CAAC;AACnD,SAAC,CAAC;;QAGF,IACE,OAAO,cAAc,KAAK,WAAW;AACrC,YAAA,OAAO,cAAc,CAAC,SAAS,KAAK,UAAU,EAC9C;AACA,YAAA,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;SAClD;;;;;;QAOD,MAAM,qBAAqB,GAAG,aAAa,CAAC;QAC5C,MAAM,aAAa,GAAG,CAAA,EAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,CAAA,GAAA,CAAK,CAAC;QAClE,cAAc,EAAE,CAAC,aAAa,GAAG,OAAM,GAAG,KAAG;YAC3C,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;;;AAG/C,gBAAA,gBAAgB,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;aAC3B;;AAED,YAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;AAC/C,gBAAA,IAAI;oBACF,qBAAqB,CAAC,GAAG,CAAC,CAAC;iBAC5B;gBAAC,OAAO,CAAC,EAAE;;AAEV,oBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAClB;aACF;AACH,SAAC,CAAC;KACH;AACF,CAAA;AAED;;;;;AAKG;AACI,MAAM,4BAA4B,GACvC,6BAA6B;AAE/B,SAAS,eAAe,GAAA;IACtB,OAAO;AACL,QAAA,IAAI,EAAuB,SAAA;AAC3B,QAAA,OAAO,EAAE,IAAI;AACb,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,WAAW,EAAE,IAAI;AACjB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE9B,qBAAY,CAA6B,eAAA,mCAAA;KACjD,CAAC;AACJ;;AC5MA;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClEK,kBAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}