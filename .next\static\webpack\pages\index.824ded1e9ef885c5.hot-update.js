"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/KanbanBoard.js":
/*!***********************************!*\
  !*** ./components/KanbanBoard.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KanbanBoard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/core */ \"./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/sortable */ \"./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @dnd-kit/utilities */ \"./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/store */ \"./lib/store.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Card */ \"./components/ui/Card.js\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/Badge */ \"./components/ui/Badge.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/Button */ \"./components/ui/Button.js\");\n/* harmony import */ var _ui_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/Modal */ \"./components/ui/Modal.js\");\n/* harmony import */ var _ui_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/Input */ \"./components/ui/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Edit,MoreHorizontal,Pause,Play,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Calendar,Clock,Edit,MoreHorizontal,Pause,Play,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _utils_exportData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../utils/exportData */ \"./utils/exportData.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TaskCard(param) {\n    let { task } = param;\n    _s();\n    const { updateTask, deleteTask } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const { activeTimer, startTimer, stopTimer } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore)();\n    const [showEdit, setShowEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editTask, setEditTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(task);\n    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = (0,_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable)({\n        id: task.id\n    });\n    const style = {\n        transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_4__.CSS.Transform.toString(transform),\n        transition\n    };\n    const handleEdit = ()=>{\n        updateTask(task.id, editTask);\n        setShowEdit(false);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task updated successfully\");\n    };\n    const handleDelete = ()=>{\n        if (window.confirm(\"Are you sure you want to delete this task?\")) {\n            deleteTask(task.id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task deleted successfully\");\n        }\n    };\n    const handleTimer = ()=>{\n        if ((activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id) {\n            stopTimer();\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer stopped\");\n        } else {\n            startTimer(task.id, task.title);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Timer started\");\n        }\n    };\n    const isTimerActive = (activeTimer === null || activeTimer === void 0 ? void 0 : activeTimer.taskId) === task.id;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: setNodeRef,\n                style: style,\n                ...attributes,\n                ...listeners,\n                className: \"mb-3 \".concat(isDragging ? \"opacity-50\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"cursor-move hover:shadow-md transition-shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            className: \"pb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: task.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleTimer();\n                                                },\n                                                children: isTimerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Pause, {\n                                                    className: \"h-3 w-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Play, {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowEdit(true);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Edit, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDelete();\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Trash2, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"pt-0\",\n                            children: [\n                                task.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400 mb-2\",\n                                    children: task.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: task.priority,\n                                                    className: \"text-xs\",\n                                                    children: task.priority\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                task.deadline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Calendar, {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.formatDate)(task.deadline)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        isTimerActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Edit_MoreHorizontal_Pause_Play_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Clock, {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Modal__WEBPACK_IMPORTED_MODULE_9__.Modal, {\n                isOpen: showEdit,\n                onClose: ()=>setShowEdit(false),\n                title: \"Edit Task\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                    value: editTask.title,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            title: e.target.value\n                                        })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: editTask.description,\n                                    onChange: (e)=>setEditTask({\n                                            ...editTask,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Priority\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: editTask.priority,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    priority: e.target.value\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"low\",\n                                                    children: \"Low\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"medium\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"high\",\n                                                    children: \"High\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"Deadline\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Input__WEBPACK_IMPORTED_MODULE_10__.Input, {\n                                            type: \"date\",\n                                            value: editTask.deadline,\n                                            onChange: (e)=>setEditTask({\n                                                    ...editTask,\n                                                    deadline: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowEdit(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    onClick: handleEdit,\n                                    children: \"Save Changes\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TaskCard, \"phrOKTed3xFlYlOxG+d3h1gAb1k=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTimeTrackingStore,\n        _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.useSortable\n    ];\n});\n_c = TaskCard;\nfunction KanbanColumn(param) {\n    let { title, status, tasks, icon: Icon, color } = param;\n    _s1();\n    const { setNodeRef, isOver } = useDroppable({\n        id: status\n    });\n    const colorClasses = {\n        blue: \"bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-100 dark:border-blue-900/30\",\n        yellow: \"bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-100 dark:border-yellow-900/30\",\n        green: \"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-100 dark:border-green-900/30\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-80\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(colorClasses[color], \" border rounded-xl p-5 transition-all duration-200 \").concat(isOver ? \"ring-2 ring-blue-400 ring-opacity-50\" : \"\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center \".concat(color === \"blue\" ? \"bg-blue-500\" : color === \"yellow\" ? \"bg-yellow-500\" : \"bg-green-500\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 dark:text-white text-lg\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            variant: \"secondary\",\n                            className: \"text-sm font-medium \".concat(color === \"blue\" ? \"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\" : color === \"yellow\" ? \"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300\" : \"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\"),\n                            children: tasks.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: setNodeRef,\n                    className: \"space-y-3 min-h-96\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.SortableContext, {\n                        items: tasks.map((task)=>task.id),\n                        strategy: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.verticalListSortingStrategy,\n                        children: [\n                            tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                                    task: task\n                                }, task.id, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)),\n                            tasks.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                                    children: 'Drop tasks here or click \"Add Task\" to get started'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s1(KanbanColumn, \"B0t40knSjPgPvDEIZEaFzamr7vc=\", true);\n_c1 = KanbanColumn;\nfunction KanbanBoard() {\n    _s2();\n    const { tasks, moveTask, currentProject, getTaskStats } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore)();\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.PointerSensor), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.KeyboardSensor, {\n        coordinateGetter: _dnd_kit_sortable__WEBPACK_IMPORTED_MODULE_3__.sortableKeyboardCoordinates\n    }));\n    const projectTasks = tasks.filter((task)=>task.project === currentProject);\n    const todoTasks = projectTasks.filter((task)=>task.status === \"todo\");\n    const inProgressTasks = projectTasks.filter((task)=>task.status === \"in-progress\");\n    const doneTasks = projectTasks.filter((task)=>task.status === \"done\");\n    const handleDragStart = (event)=>{\n        setActiveId(event.active.id);\n    };\n    const handleDragEnd = (event)=>{\n        var _over_data_current_sortable, _over_data_current, _over_data;\n        const { active, over } = event;\n        setActiveId(null);\n        if (!over) return;\n        const activeTask = projectTasks.find((task)=>task.id === active.id);\n        if (!activeTask) return;\n        const overContainer = ((_over_data = over.data) === null || _over_data === void 0 ? void 0 : (_over_data_current = _over_data.current) === null || _over_data_current === void 0 ? void 0 : (_over_data_current_sortable = _over_data_current.sortable) === null || _over_data_current_sortable === void 0 ? void 0 : _over_data_current_sortable.containerId) || over.id;\n        let newStatus = activeTask.status;\n        if (overContainer.includes(\"todo\")) newStatus = \"todo\";\n        else if (overContainer.includes(\"in-progress\")) newStatus = \"in-progress\";\n        else if (overContainer.includes(\"done\")) newStatus = \"done\";\n        if (newStatus !== activeTask.status) {\n            moveTask(active.id, newStatus);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Task moved to \".concat(newStatus.replace(\"-\", \" \")));\n        }\n    };\n    const activeTask = activeId ? projectTasks.find((task)=>task.id === activeId) : null;\n    const handleExportCSV = ()=>{\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToCSV)(projectTasks, \"\".concat(currentProject, \"-kanban-tasks\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to CSV successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    const handleExportPDF = ()=>{\n        const stats = getTaskStats();\n        const success = (0,_utils_exportData__WEBPACK_IMPORTED_MODULE_12__.exportTasksToPDF)(projectTasks, stats, \"\".concat(currentProject, \"-kanban-report\"));\n        if (success) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].success(\"Tasks exported to PDF successfully!\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_13__[\"default\"].error(\"Failed to export tasks\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Kanban Board\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Drag and drop tasks between columns to update their status\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportCSV,\n                                    children: \"Export CSV\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleExportPDF,\n                                    children: \"Export PDF\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DndContext, {\n                sensors: sensors,\n                collisionDetection: _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.closestCorners,\n                onDragStart: handleDragStart,\n                onDragEnd: handleDragEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 overflow-x-auto pb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"To Do\",\n                                status: \"todo\",\n                                tasks: todoTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"In Progress\",\n                                status: \"in-progress\",\n                                tasks: inProgressTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KanbanColumn, {\n                                title: \"Done\",\n                                status: \"done\",\n                                tasks: doneTasks\n                            }, void 0, false, {\n                                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.DragOverlay, {\n                        children: activeTask ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TaskCard, {\n                            task: activeTask\n                        }, void 0, false, {\n                            fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                            lineNumber: 385,\n                            columnNumber: 25\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\code-files\\\\ai files\\\\to-do\\\\components\\\\KanbanBoard.js\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, this);\n}\n_s2(KanbanBoard, \"dKIcBJsvhRbKqNVhbc0ByDX8JMo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_5__.useTaskStore,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_2__.useSensors\n    ];\n});\n_c2 = KanbanBoard;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TaskCard\");\n$RefreshReg$(_c1, \"KanbanColumn\");\n$RefreshReg$(_c2, \"KanbanBoard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0thbmJhbkJvYXJkLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlDO0FBU1Y7QUFNSTtBQUdBO0FBQ2M7QUFDeUI7QUFDRztBQUNsQztBQUNFO0FBQ0Y7QUFDQTtBQVNiO0FBQ3NDO0FBQ2E7QUFDckM7QUFDcEMsU0FBU29DLFNBQVMsS0FBUTtRQUFSLEVBQUVDLElBQUksRUFBRSxHQUFSOztJQUNoQixNQUFNLEVBQUVDLFVBQVUsRUFBRUMsVUFBVSxFQUFFLEdBQUd6Qix3REFBWUE7SUFDL0MsTUFBTSxFQUFFMEIsV0FBVyxFQUFFQyxVQUFVLEVBQUVDLFNBQVMsRUFBRSxHQUFHM0IsZ0VBQW9CQTtJQUNuRSxNQUFNLENBQUM0QixVQUFVQyxZQUFZLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUM2QyxVQUFVQyxZQUFZLEdBQUc5QywrQ0FBUUEsQ0FBQ3FDO0lBQ3pDLE1BQU0sRUFDSlUsVUFBVSxFQUNWQyxTQUFTLEVBQ1RDLFVBQVUsRUFDVkMsU0FBUyxFQUNUQyxVQUFVLEVBQ1ZDLFVBQVUsRUFDWCxHQUFHeEMsOERBQVdBLENBQUM7UUFBRXlDLElBQUloQixLQUFLZ0IsRUFBRTtJQUFDO0lBQzlCLE1BQU1DLFFBQVE7UUFDWkosV0FBV3JDLG1EQUFHQSxDQUFDMEMsU0FBUyxDQUFDQyxRQUFRLENBQUNOO1FBQ2xDQztJQUNGO0lBQ0EsTUFBTU0sYUFBYTtRQUNqQm5CLFdBQVdELEtBQUtnQixFQUFFLEVBQUVSO1FBQ3BCRCxZQUFZO1FBQ1pULGdFQUFhLENBQUM7SUFDaEI7SUFDQSxNQUFNd0IsZUFBZTtRQUNuQixJQUFJQyxPQUFPQyxPQUFPLENBQUMsK0NBQStDO1lBQ2hFdEIsV0FBV0YsS0FBS2dCLEVBQUU7WUFDbEJsQixnRUFBYSxDQUFDO1FBQ2hCO0lBQ0Y7SUFDQSxNQUFNMkIsY0FBYztRQUNsQixJQUFJdEIsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhdUIsTUFBTSxNQUFLMUIsS0FBS2dCLEVBQUUsRUFBRTtZQUNuQ1g7WUFDQVAsZ0VBQWEsQ0FBQztRQUNoQixPQUFPO1lBQ0xNLFdBQVdKLEtBQUtnQixFQUFFLEVBQUVoQixLQUFLMkIsS0FBSztZQUM5QjdCLGdFQUFhLENBQUM7UUFDaEI7SUFDRjtJQUNBLE1BQU04QixnQkFBZ0J6QixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWF1QixNQUFNLE1BQUsxQixLQUFLZ0IsRUFBRTtJQUNyRCxxQkFDRTs7MEJBQ0UsOERBQUNhO2dCQUNDQyxLQUFLbEI7Z0JBQ0xLLE9BQU9BO2dCQUNOLEdBQUdQLFVBQVU7Z0JBQ2IsR0FBR0MsU0FBUztnQkFDYm9CLFdBQVcsUUFBdUMsT0FBL0JoQixhQUFhLGVBQWU7MEJBRS9DLDRFQUFDcEMsMENBQUlBO29CQUFDb0QsV0FBVTs7c0NBQ2QsOERBQUNsRCxnREFBVUE7NEJBQUNrRCxXQUFVO3NDQUNwQiw0RUFBQ0Y7Z0NBQUlFLFdBQVU7O2tEQUNiLDhEQUFDakQsK0NBQVNBO3dDQUFDaUQsV0FBVTtrREFBdUIvQixLQUFLMkIsS0FBSzs7Ozs7O2tEQUN0RCw4REFBQ0U7d0NBQUlFLFdBQVU7OzBEQUNiLDhEQUFDL0MsOENBQU1BO2dEQUNMZ0QsU0FBUTtnREFDUkMsTUFBSztnREFDTEYsV0FBVTtnREFDVkcsU0FBUyxDQUFDQztvREFDUkEsRUFBRUMsZUFBZTtvREFDakJYO2dEQUNGOzBEQUVDRyw4QkFDQyw4REFBQ3BDLDRIQUFLQTtvREFBQ3VDLFdBQVU7Ozs7O3lFQUVqQiw4REFBQ3hDLDJIQUFJQTtvREFBQ3dDLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdwQiw4REFBQy9DLDhDQUFNQTtnREFDTGdELFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xGLFdBQVU7Z0RBQ1ZHLFNBQVMsQ0FBQ0M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCN0IsWUFBWTtnREFDZDswREFFQSw0RUFBQ2xCLDJIQUFJQTtvREFBQzBDLFdBQVU7Ozs7Ozs7Ozs7OzBEQUVsQiw4REFBQy9DLDhDQUFNQTtnREFDTGdELFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xGLFdBQVU7Z0RBQ1ZHLFNBQVMsQ0FBQ0M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCZDtnREFDRjswREFFQSw0RUFBQ2hDLDZIQUFNQTtvREFBQ3lDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzFCLDhEQUFDbkQsaURBQVdBOzRCQUFDbUQsV0FBVTs7Z0NBQ3BCL0IsS0FBS3FDLFdBQVcsa0JBQ2YsOERBQUNDO29DQUFFUCxXQUFVOzhDQUNWL0IsS0FBS3FDLFdBQVc7Ozs7Ozs4Q0FHckIsOERBQUNSO29DQUFJRSxXQUFVOztzREFDYiw4REFBQ0Y7NENBQUlFLFdBQVU7OzhEQUNiLDhEQUFDaEQsNENBQUtBO29EQUFDaUQsU0FBU2hDLEtBQUt1QyxRQUFRO29EQUFFUixXQUFVOzhEQUN0Qy9CLEtBQUt1QyxRQUFROzs7Ozs7Z0RBRWZ2QyxLQUFLd0MsUUFBUSxrQkFDWiw4REFBQ1g7b0RBQUlFLFdBQVU7O3NFQUNiLDhEQUFDM0MsK0hBQVFBOzREQUFDMkMsV0FBVTs7Ozs7O3dEQUNuQnJDLHVEQUFVQSxDQUFDTSxLQUFLd0MsUUFBUTs7Ozs7Ozs7Ozs7Ozt3Q0FJOUJaLCtCQUNDLDhEQUFDQzs0Q0FBSUUsV0FBVTs7OERBQ2IsOERBQUM1Qyw0SEFBS0E7b0RBQUM0QyxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUTlDLDhEQUFDOUMsNENBQUtBO2dCQUNKd0QsUUFBUW5DO2dCQUNSb0MsU0FBUyxJQUFNbkMsWUFBWTtnQkFDM0JvQixPQUFNOzBCQUVOLDRFQUFDRTtvQkFBSUUsV0FBVTs7c0NBQ2IsOERBQUNGOzs4Q0FDQyw4REFBQ2M7b0NBQU1aLFdBQVU7OENBQWtFOzs7Ozs7OENBR25GLDhEQUFDN0MsNkNBQUtBO29DQUNKMEQsT0FBT3BDLFNBQVNtQixLQUFLO29DQUNyQmtCLFVBQVUsQ0FBQ1YsSUFBTTFCLFlBQVk7NENBQUUsR0FBR0QsUUFBUTs0Q0FBRW1CLE9BQU9RLEVBQUVXLE1BQU0sQ0FBQ0YsS0FBSzt3Q0FBQzs7Ozs7Ozs7Ozs7O3NDQUd0RSw4REFBQ2Y7OzhDQUNDLDhEQUFDYztvQ0FBTVosV0FBVTs4Q0FBa0U7Ozs7Ozs4Q0FHbkYsOERBQUNnQjtvQ0FDQ0gsT0FBT3BDLFNBQVM2QixXQUFXO29DQUMzQlEsVUFBVSxDQUFDVixJQUFNMUIsWUFBWTs0Q0FBRSxHQUFHRCxRQUFROzRDQUFFNkIsYUFBYUYsRUFBRVcsTUFBTSxDQUFDRixLQUFLO3dDQUFDO29DQUN4RWIsV0FBVTtvQ0FDVmlCLE1BQU07Ozs7Ozs7Ozs7OztzQ0FHViw4REFBQ25COzRCQUFJRSxXQUFVOzs4Q0FDYiw4REFBQ0Y7O3NEQUNDLDhEQUFDYzs0Q0FBTVosV0FBVTtzREFBa0U7Ozs7OztzREFHbkYsOERBQUNrQjs0Q0FDQ0wsT0FBT3BDLFNBQVMrQixRQUFROzRDQUN4Qk0sVUFBVSxDQUFDVixJQUFNMUIsWUFBWTtvREFBRSxHQUFHRCxRQUFRO29EQUFFK0IsVUFBVUosRUFBRVcsTUFBTSxDQUFDRixLQUFLO2dEQUFDOzRDQUNyRWIsV0FBVTs7OERBRVYsOERBQUNtQjtvREFBT04sT0FBTTs4REFBTTs7Ozs7OzhEQUNwQiw4REFBQ007b0RBQU9OLE9BQU07OERBQVM7Ozs7Ozs4REFDdkIsOERBQUNNO29EQUFPTixPQUFNOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pCLDhEQUFDZjs7c0RBQ0MsOERBQUNjOzRDQUFNWixXQUFVO3NEQUFrRTs7Ozs7O3NEQUduRiw4REFBQzdDLDZDQUFLQTs0Q0FDSmlFLE1BQUs7NENBQ0xQLE9BQU9wQyxTQUFTZ0MsUUFBUTs0Q0FDeEJLLFVBQVUsQ0FBQ1YsSUFBTTFCLFlBQVk7b0RBQUUsR0FBR0QsUUFBUTtvREFBRWdDLFVBQVVMLEVBQUVXLE1BQU0sQ0FBQ0YsS0FBSztnREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUkzRSw4REFBQ2Y7NEJBQUlFLFdBQVU7OzhDQUNiLDhEQUFDL0MsOENBQU1BO29DQUFDZ0QsU0FBUTtvQ0FBVUUsU0FBUyxJQUFNM0IsWUFBWTs4Q0FBUTs7Ozs7OzhDQUc3RCw4REFBQ3ZCLDhDQUFNQTtvQ0FBQ2tELFNBQVNkOzhDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXpDO0dBeExTckI7O1FBQzRCdEIsb0RBQVlBO1FBQ0FDLDREQUFvQkE7UUFVL0RILDBEQUFXQTs7O0tBWlJ3QjtBQXlMVCxTQUFTcUQsYUFBYSxLQUEyQztRQUEzQyxFQUFFekIsS0FBSyxFQUFFMEIsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLE1BQU1DLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQTNDOztJQUNwQixNQUFNLEVBQ0o3QyxVQUFVLEVBQ1Y4QyxNQUFNLEVBQ1AsR0FBR0MsYUFBYTtRQUNmM0MsSUFBSXFDO0lBQ047SUFFQSxNQUFNTyxlQUFlO1FBQ25CQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNsQztRQUFJRSxXQUFVO2tCQUNiLDRFQUFDRjtZQUFJRSxXQUFXLEdBQTRFMkIsT0FBekVFLFlBQVksQ0FBQ0gsTUFBTSxFQUFDLHVEQUEwRyxPQUFyREMsU0FBUyx5Q0FBeUM7OzhCQUM1SSw4REFBQzdCO29CQUFJRSxXQUFVOztzQ0FDYiw4REFBQ0Y7NEJBQUlFLFdBQVU7OzhDQUNiLDhEQUFDRjtvQ0FBSUUsV0FBVyx1REFHZixPQUZDMEIsVUFBVSxTQUFTLGdCQUNuQkEsVUFBVSxXQUFXLGtCQUFrQjs4Q0FFdkMsNEVBQUNEO3dDQUFLekIsV0FBVTs7Ozs7Ozs7Ozs7OENBRWxCLDhEQUFDaUM7b0NBQUdqQyxXQUFVOzhDQUNYSjs7Ozs7Ozs7Ozs7O3NDQUdMLDhEQUFDNUMsNENBQUtBOzRCQUNKaUQsU0FBUTs0QkFDUkQsV0FBVyx1QkFJVixPQUhDMEIsVUFBVSxTQUFTLHFFQUNuQkEsVUFBVSxXQUFXLDZFQUNyQjtzQ0FHREgsTUFBTVcsTUFBTTs7Ozs7Ozs7Ozs7OzhCQUlqQiw4REFBQ3BDO29CQUNDQyxLQUFLbEI7b0JBQ0xtQixXQUFVOzhCQUVWLDRFQUFDM0QsOERBQWVBO3dCQUFDOEYsT0FBT1osTUFBTWEsR0FBRyxDQUFDbkUsQ0FBQUEsT0FBUUEsS0FBS2dCLEVBQUU7d0JBQUdvRCxVQUFVOUYsMEVBQTJCQTs7NEJBQ3RGZ0YsTUFBTWEsR0FBRyxDQUFDLENBQUNuRSxxQkFDViw4REFBQ0Q7b0NBQXVCQyxNQUFNQTttQ0FBZkEsS0FBS2dCLEVBQUU7Ozs7OzRCQUV2QnNDLE1BQU1XLE1BQU0sS0FBSyxtQkFDaEIsOERBQUNwQztnQ0FBSUUsV0FBVTswQ0FDYiw0RUFBQ087b0NBQUVQLFdBQVU7OENBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVeEU7SUE3RFNxQjtNQUFBQTtBQThETSxTQUFTaUI7O0lBQ3RCLE1BQU0sRUFBRWYsS0FBSyxFQUFFZ0IsUUFBUSxFQUFFQyxjQUFjLEVBQUVDLFlBQVksRUFBRSxHQUFHL0Ysd0RBQVlBO0lBQ3RFLE1BQU0sQ0FBQ2dHLFVBQVVDLFlBQVksR0FBRy9HLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU1nSCxVQUFVekcseURBQVVBLENBQ3hCRCx3REFBU0EsQ0FBQ0Qsd0RBQWFBLEdBQ3ZCQyx3REFBU0EsQ0FBQ0YseURBQWNBLEVBQUU7UUFDeEI2RyxrQkFBa0J2RywwRUFBMkJBO0lBQy9DO0lBRUYsTUFBTXdHLGVBQWV2QixNQUFNd0IsTUFBTSxDQUFDOUUsQ0FBQUEsT0FBUUEsS0FBSytFLE9BQU8sS0FBS1I7SUFDM0QsTUFBTVMsWUFBWUgsYUFBYUMsTUFBTSxDQUFDOUUsQ0FBQUEsT0FBUUEsS0FBS3FELE1BQU0sS0FBSztJQUM5RCxNQUFNNEIsa0JBQWtCSixhQUFhQyxNQUFNLENBQUM5RSxDQUFBQSxPQUFRQSxLQUFLcUQsTUFBTSxLQUFLO0lBQ3BFLE1BQU02QixZQUFZTCxhQUFhQyxNQUFNLENBQUM5RSxDQUFBQSxPQUFRQSxLQUFLcUQsTUFBTSxLQUFLO0lBQzlELE1BQU04QixrQkFBa0IsQ0FBQ0M7UUFDdkJWLFlBQVlVLE1BQU1DLE1BQU0sQ0FBQ3JFLEVBQUU7SUFDN0I7SUFDQSxNQUFNc0UsZ0JBQWdCLENBQUNGO1lBTUNHLDZCQUFBQSxvQkFBQUE7UUFMdEIsTUFBTSxFQUFFRixNQUFNLEVBQUVFLElBQUksRUFBRSxHQUFHSDtRQUN6QlYsWUFBWTtRQUNaLElBQUksQ0FBQ2EsTUFBTTtRQUNYLE1BQU1DLGFBQWFYLGFBQWFZLElBQUksQ0FBQ3pGLENBQUFBLE9BQVFBLEtBQUtnQixFQUFFLEtBQUtxRSxPQUFPckUsRUFBRTtRQUNsRSxJQUFJLENBQUN3RSxZQUFZO1FBQ2pCLE1BQU1FLGdCQUFnQkgsRUFBQUEsYUFBQUEsS0FBS0ksSUFBSSxjQUFUSixrQ0FBQUEscUJBQUFBLFdBQVdLLE9BQU8sY0FBbEJMLDBDQUFBQSw4QkFBQUEsbUJBQW9CTSxRQUFRLGNBQTVCTixrREFBQUEsNEJBQThCTyxXQUFXLEtBQUlQLEtBQUt2RSxFQUFFO1FBQzFFLElBQUkrRSxZQUFZUCxXQUFXbkMsTUFBTTtRQUNqQyxJQUFJcUMsY0FBY00sUUFBUSxDQUFDLFNBQVNELFlBQVk7YUFDM0MsSUFBSUwsY0FBY00sUUFBUSxDQUFDLGdCQUFnQkQsWUFBWTthQUN2RCxJQUFJTCxjQUFjTSxRQUFRLENBQUMsU0FBU0QsWUFBWTtRQUNyRCxJQUFJQSxjQUFjUCxXQUFXbkMsTUFBTSxFQUFFO1lBQ25DaUIsU0FBU2UsT0FBT3JFLEVBQUUsRUFBRStFO1lBQ3BCakcsZ0VBQWEsQ0FBQyxpQkFBNkMsT0FBNUJpRyxVQUFVRSxPQUFPLENBQUMsS0FBSztRQUN4RDtJQUNGO0lBQ0EsTUFBTVQsYUFBYWYsV0FBV0ksYUFBYVksSUFBSSxDQUFDekYsQ0FBQUEsT0FBUUEsS0FBS2dCLEVBQUUsS0FBS3lELFlBQVk7SUFFaEYsTUFBTXlCLGtCQUFrQjtRQUN0QixNQUFNN0UsVUFBVXpCLG9FQUFnQkEsQ0FBQ2lGLGNBQWMsR0FBa0IsT0FBZk4sZ0JBQWU7UUFDakUsSUFBSWxELFNBQVM7WUFDWHZCLGdFQUFhLENBQUM7UUFDaEIsT0FBTztZQUNMQSw4REFBVyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1zRyxrQkFBa0I7UUFDdEIsTUFBTUMsUUFBUTdCO1FBQ2QsTUFBTW5ELFVBQVV4QixvRUFBZ0JBLENBQUNnRixjQUFjd0IsT0FBTyxHQUFrQixPQUFmOUIsZ0JBQWU7UUFDeEUsSUFBSWxELFNBQVM7WUFDWHZCLGdFQUFhLENBQUM7UUFDaEIsT0FBTztZQUNMQSw4REFBVyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLHFCQUNFLDhEQUFDK0I7UUFBSUUsV0FBVTs7MEJBQ2IsOERBQUNGO2dCQUFJRSxXQUFVOzBCQUNiLDRFQUFDRjtvQkFBSUUsV0FBVTs7c0NBQ2IsOERBQUNGOzs4Q0FDQyw4REFBQ3lFO29DQUFHdkUsV0FBVTs4Q0FBbUQ7Ozs7Ozs4Q0FHakUsOERBQUNPO29DQUFFUCxXQUFVOzhDQUFtQzs7Ozs7Ozs7Ozs7O3NDQUlsRCw4REFBQ0Y7NEJBQUlFLFdBQVU7OzhDQUNiLDhEQUFDL0MsOENBQU1BO29DQUFDZ0QsU0FBUTtvQ0FBVUMsTUFBSztvQ0FBS0MsU0FBU2dFOzhDQUFpQjs7Ozs7OzhDQUc5RCw4REFBQ2xILDhDQUFNQTtvQ0FBQ2dELFNBQVE7b0NBQVVDLE1BQUs7b0NBQUtDLFNBQVNrRTs4Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1wRSw4REFBQ3hJLHFEQUFVQTtnQkFDVCtHLFNBQVNBO2dCQUNUNEIsb0JBQW9CekkseURBQWNBO2dCQUNsQzBJLGFBQWFyQjtnQkFDYnNCLFdBQVduQjs7a0NBRVgsOERBQUN6RDt3QkFBSUUsV0FBVTs7MENBQ2IsOERBQUNxQjtnQ0FDQ3pCLE9BQU07Z0NBQ04wQixRQUFPO2dDQUNQQyxPQUFPMEI7Ozs7OzswQ0FFVCw4REFBQzVCO2dDQUNDekIsT0FBTTtnQ0FDTjBCLFFBQU87Z0NBQ1BDLE9BQU8yQjs7Ozs7OzBDQUVULDhEQUFDN0I7Z0NBQ0N6QixPQUFNO2dDQUNOMEIsUUFBTztnQ0FDUEMsT0FBTzRCOzs7Ozs7Ozs7Ozs7a0NBR1gsOERBQUNySCxzREFBV0E7a0NBQ1QySCwyQkFBYSw4REFBQ3pGOzRCQUFTQyxNQUFNd0Y7Ozs7O21DQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pEO0lBeEd3Qm5COztRQUNvQzVGLG9EQUFZQTtRQUV0RFAscURBQVVBOzs7TUFISm1HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvS2FuYmFuQm9hcmQuanM/ZWJlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIERuZENvbnRleHQsXG4gIERyYWdPdmVybGF5LFxuICBjbG9zZXN0Q29ybmVycyxcbiAgS2V5Ym9hcmRTZW5zb3IsXG4gIFBvaW50ZXJTZW5zb3IsXG4gIHVzZVNlbnNvcixcbiAgdXNlU2Vuc29ycyxcbn0gZnJvbSAnQGRuZC1raXQvY29yZSc7XG5pbXBvcnQge1xuICBhcnJheU1vdmUsXG4gIFNvcnRhYmxlQ29udGV4dCxcbiAgc29ydGFibGVLZXlib2FyZENvb3JkaW5hdGVzLFxuICB2ZXJ0aWNhbExpc3RTb3J0aW5nU3RyYXRlZ3ksXG59IGZyb20gJ0BkbmQta2l0L3NvcnRhYmxlJztcbmltcG9ydCB7XG4gIHVzZVNvcnRhYmxlLFxufSBmcm9tICdAZG5kLWtpdC9zb3J0YWJsZSc7XG5pbXBvcnQgeyBDU1MgfSBmcm9tICdAZG5kLWtpdC91dGlsaXRpZXMnO1xuaW1wb3J0IHsgdXNlVGFza1N0b3JlLCB1c2VUaW1lVHJhY2tpbmdTdG9yZSB9IGZyb20gJy4uL2xpYi9zdG9yZSc7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnLi91aS9DYXJkJztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnLi91aS9CYWRnZSc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICcuL3VpL0J1dHRvbic7XG5pbXBvcnQgeyBNb2RhbCB9IGZyb20gJy4vdWkvTW9kYWwnO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICcuL3VpL0lucHV0JztcbmltcG9ydCB7IFxuICBDbG9jaywgXG4gIENhbGVuZGFyLCBcbiAgRWRpdCwgXG4gIFRyYXNoMiwgXG4gIFBsYXksIFxuICBQYXVzZSxcbiAgTW9yZUhvcml6b250YWwgXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBmb3JtYXREYXRlLCBnZXRQcmlvcml0eUNvbG9yIH0gZnJvbSAnLi4vbGliL3V0aWxzJztcbmltcG9ydCB7IGV4cG9ydFRhc2tzVG9DU1YsIGV4cG9ydFRhc2tzVG9QREYgfSBmcm9tICcuLi91dGlscy9leHBvcnREYXRhJztcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuZnVuY3Rpb24gVGFza0NhcmQoeyB0YXNrIH0pIHtcbiAgY29uc3QgeyB1cGRhdGVUYXNrLCBkZWxldGVUYXNrIH0gPSB1c2VUYXNrU3RvcmUoKTtcbiAgY29uc3QgeyBhY3RpdmVUaW1lciwgc3RhcnRUaW1lciwgc3RvcFRpbWVyIH0gPSB1c2VUaW1lVHJhY2tpbmdTdG9yZSgpO1xuICBjb25zdCBbc2hvd0VkaXQsIHNldFNob3dFZGl0XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRUYXNrLCBzZXRFZGl0VGFza10gPSB1c2VTdGF0ZSh0YXNrKTtcbiAgY29uc3Qge1xuICAgIGF0dHJpYnV0ZXMsXG4gICAgbGlzdGVuZXJzLFxuICAgIHNldE5vZGVSZWYsXG4gICAgdHJhbnNmb3JtLFxuICAgIHRyYW5zaXRpb24sXG4gICAgaXNEcmFnZ2luZyxcbiAgfSA9IHVzZVNvcnRhYmxlKHsgaWQ6IHRhc2suaWQgfSk7XG4gIGNvbnN0IHN0eWxlID0ge1xuICAgIHRyYW5zZm9ybTogQ1NTLlRyYW5zZm9ybS50b1N0cmluZyh0cmFuc2Zvcm0pLFxuICAgIHRyYW5zaXRpb24sXG4gIH07XG4gIGNvbnN0IGhhbmRsZUVkaXQgPSAoKSA9PiB7XG4gICAgdXBkYXRlVGFzayh0YXNrLmlkLCBlZGl0VGFzayk7XG4gICAgc2V0U2hvd0VkaXQoZmFsc2UpO1xuICAgIHRvYXN0LnN1Y2Nlc3MoJ1Rhc2sgdXBkYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgfTtcbiAgY29uc3QgaGFuZGxlRGVsZXRlID0gKCkgPT4ge1xuICAgIGlmICh3aW5kb3cuY29uZmlybSgnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHRhc2s/JykpIHtcbiAgICAgIGRlbGV0ZVRhc2sodGFzay5pZCk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdUYXNrIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgfVxuICB9O1xuICBjb25zdCBoYW5kbGVUaW1lciA9ICgpID0+IHtcbiAgICBpZiAoYWN0aXZlVGltZXI/LnRhc2tJZCA9PT0gdGFzay5pZCkge1xuICAgICAgc3RvcFRpbWVyKCk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdUaW1lciBzdG9wcGVkJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHN0YXJ0VGltZXIodGFzay5pZCwgdGFzay50aXRsZSk7XG4gICAgICB0b2FzdC5zdWNjZXNzKCdUaW1lciBzdGFydGVkJyk7XG4gICAgfVxuICB9O1xuICBjb25zdCBpc1RpbWVyQWN0aXZlID0gYWN0aXZlVGltZXI/LnRhc2tJZCA9PT0gdGFzay5pZDtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPGRpdlxuICAgICAgICByZWY9e3NldE5vZGVSZWZ9XG4gICAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgICAgey4uLmF0dHJpYnV0ZXN9XG4gICAgICAgIHsuLi5saXN0ZW5lcnN9XG4gICAgICAgIGNsYXNzTmFtZT17YG1iLTMgJHtpc0RyYWdnaW5nID8gJ29wYWNpdHktNTAnIDogJyd9YH1cbiAgICAgID5cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiY3Vyc29yLW1vdmUgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93XCI+XG4gICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj57dGFzay50aXRsZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNlwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICBoYW5kbGVUaW1lcigpO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNUaW1lckFjdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPFBhdXNlIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC02IHctNlwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICBzZXRTaG93RWRpdCh0cnVlKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1yZWQtNTAwIGhvdmVyOnRleHQtcmVkLTcwMFwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGUoKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB0LTBcIj5cbiAgICAgICAgICAgIHt0YXNrLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAge3Rhc2suZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PXt0YXNrLnByaW9yaXR5fSBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICB7dGFzay5wcmlvcml0eX1cbiAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgIHt0YXNrLmRlYWRsaW5lICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZSh0YXNrLmRlYWRsaW5lKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7aXNUaW1lckFjdGl2ZSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXhzIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgIEFjdGl2ZVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgICA8TW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93RWRpdH1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0VkaXQoZmFsc2UpfVxuICAgICAgICB0aXRsZT1cIkVkaXQgVGFza1wiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgVGl0bGVcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgdmFsdWU9e2VkaXRUYXNrLnRpdGxlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRUYXNrKHsgLi4uZWRpdFRhc2ssIHRpdGxlOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgdmFsdWU9e2VkaXRUYXNrLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRUYXNrKHsgLi4uZWRpdFRhc2ssIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIFByaW9yaXR5XG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdFRhc2sucHJpb3JpdHl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0VGFzayh7IC4uLmVkaXRUYXNrLCBwcmlvcml0eTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibG93XCI+TG93PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1lZGl1bVwiPk1lZGl1bTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJoaWdoXCI+SGlnaDwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIERlYWRsaW5lXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdFRhc2suZGVhZGxpbmV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0VGFzayh7IC4uLmVkaXRUYXNrLCBkZWFkbGluZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0yIHB0LTRcIj5cbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RWRpdChmYWxzZSl9PlxuICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVFZGl0fT5cbiAgICAgICAgICAgICAgU2F2ZSBDaGFuZ2VzXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L01vZGFsPlxuICAgIDwvPlxuICApO1xufVxuZnVuY3Rpb24gS2FuYmFuQ29sdW1uKHsgdGl0bGUsIHN0YXR1cywgdGFza3MsIGljb246IEljb24sIGNvbG9yIH0pIHtcbiAgY29uc3Qge1xuICAgIHNldE5vZGVSZWYsXG4gICAgaXNPdmVyLFxuICB9ID0gdXNlRHJvcHBhYmxlKHtcbiAgICBpZDogc3RhdHVzLFxuICB9KTtcblxuICBjb25zdCBjb2xvckNsYXNzZXMgPSB7XG4gICAgYmx1ZTogJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tNTAgZGFyazpmcm9tLWJsdWUtOTUwLzIwIGRhcms6dG8taW5kaWdvLTk1MC8yMCBib3JkZXItYmx1ZS0xMDAgZGFyazpib3JkZXItYmx1ZS05MDAvMzAnLFxuICAgIHllbGxvdzogJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20teWVsbG93LTUwIHRvLW9yYW5nZS01MCBkYXJrOmZyb20teWVsbG93LTk1MC8yMCBkYXJrOnRvLW9yYW5nZS05NTAvMjAgYm9yZGVyLXllbGxvdy0xMDAgZGFyazpib3JkZXIteWVsbG93LTkwMC8zMCcsXG4gICAgZ3JlZW46ICdiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwIHRvLWVtZXJhbGQtNTAgZGFyazpmcm9tLWdyZWVuLTk1MC8yMCBkYXJrOnRvLWVtZXJhbGQtOTUwLzIwIGJvcmRlci1ncmVlbi0xMDAgZGFyazpib3JkZXItZ3JlZW4tOTAwLzMwJ1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctODBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtjb2xvckNsYXNzZXNbY29sb3JdfSBib3JkZXIgcm91bmRlZC14bCBwLTUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7aXNPdmVyID8gJ3JpbmctMiByaW5nLWJsdWUtNDAwIHJpbmctb3BhY2l0eS01MCcgOiAnJ31gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctOCBoLTggcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciAke1xuICAgICAgICAgICAgICBjb2xvciA9PT0gJ2JsdWUnID8gJ2JnLWJsdWUtNTAwJyA6XG4gICAgICAgICAgICAgIGNvbG9yID09PSAneWVsbG93JyA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1ncmVlbi01MDAnXG4gICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAge3RpdGxlfVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICBjb2xvciA9PT0gJ2JsdWUnID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAgZGFyazpiZy1ibHVlLTkwMC8zMCBkYXJrOnRleHQtYmx1ZS0zMDAnIDpcbiAgICAgICAgICAgICAgY29sb3IgPT09ICd5ZWxsb3cnID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctNzAwIGRhcms6YmcteWVsbG93LTkwMC8zMCBkYXJrOnRleHQteWVsbG93LTMwMCcgOlxuICAgICAgICAgICAgICAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNzAwIGRhcms6YmctZ3JlZW4tOTAwLzMwIGRhcms6dGV4dC1ncmVlbi0zMDAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7dGFza3MubGVuZ3RofVxuICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXZcbiAgICAgICAgICByZWY9e3NldE5vZGVSZWZ9XG4gICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS0zIG1pbi1oLTk2XCJcbiAgICAgICAgPlxuICAgICAgICAgIDxTb3J0YWJsZUNvbnRleHQgaXRlbXM9e3Rhc2tzLm1hcCh0YXNrID0+IHRhc2suaWQpfSBzdHJhdGVneT17dmVydGljYWxMaXN0U29ydGluZ1N0cmF0ZWd5fT5cbiAgICAgICAgICAgIHt0YXNrcy5tYXAoKHRhc2spID0+IChcbiAgICAgICAgICAgICAgPFRhc2tDYXJkIGtleT17dGFzay5pZH0gdGFzaz17dGFza30gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgICAge3Rhc2tzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0zMiBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgRHJvcCB0YXNrcyBoZXJlIG9yIGNsaWNrIFwiQWRkIFRhc2tcIiB0byBnZXQgc3RhcnRlZFxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvU29ydGFibGVDb250ZXh0PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gS2FuYmFuQm9hcmQoKSB7XG4gIGNvbnN0IHsgdGFza3MsIG1vdmVUYXNrLCBjdXJyZW50UHJvamVjdCwgZ2V0VGFza1N0YXRzIH0gPSB1c2VUYXNrU3RvcmUoKTtcbiAgY29uc3QgW2FjdGl2ZUlkLCBzZXRBY3RpdmVJZF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3Qgc2Vuc29ycyA9IHVzZVNlbnNvcnMoXG4gICAgdXNlU2Vuc29yKFBvaW50ZXJTZW5zb3IpLFxuICAgIHVzZVNlbnNvcihLZXlib2FyZFNlbnNvciwge1xuICAgICAgY29vcmRpbmF0ZUdldHRlcjogc29ydGFibGVLZXlib2FyZENvb3JkaW5hdGVzLFxuICAgIH0pXG4gICk7XG4gIGNvbnN0IHByb2plY3RUYXNrcyA9IHRhc2tzLmZpbHRlcih0YXNrID0+IHRhc2sucHJvamVjdCA9PT0gY3VycmVudFByb2plY3QpO1xuICBjb25zdCB0b2RvVGFza3MgPSBwcm9qZWN0VGFza3MuZmlsdGVyKHRhc2sgPT4gdGFzay5zdGF0dXMgPT09ICd0b2RvJyk7XG4gIGNvbnN0IGluUHJvZ3Jlc3NUYXNrcyA9IHByb2plY3RUYXNrcy5maWx0ZXIodGFzayA9PiB0YXNrLnN0YXR1cyA9PT0gJ2luLXByb2dyZXNzJyk7XG4gIGNvbnN0IGRvbmVUYXNrcyA9IHByb2plY3RUYXNrcy5maWx0ZXIodGFzayA9PiB0YXNrLnN0YXR1cyA9PT0gJ2RvbmUnKTtcbiAgY29uc3QgaGFuZGxlRHJhZ1N0YXJ0ID0gKGV2ZW50KSA9PiB7XG4gICAgc2V0QWN0aXZlSWQoZXZlbnQuYWN0aXZlLmlkKTtcbiAgfTtcbiAgY29uc3QgaGFuZGxlRHJhZ0VuZCA9IChldmVudCkgPT4ge1xuICAgIGNvbnN0IHsgYWN0aXZlLCBvdmVyIH0gPSBldmVudDtcbiAgICBzZXRBY3RpdmVJZChudWxsKTtcbiAgICBpZiAoIW92ZXIpIHJldHVybjtcbiAgICBjb25zdCBhY3RpdmVUYXNrID0gcHJvamVjdFRhc2tzLmZpbmQodGFzayA9PiB0YXNrLmlkID09PSBhY3RpdmUuaWQpO1xuICAgIGlmICghYWN0aXZlVGFzaykgcmV0dXJuO1xuICAgIGNvbnN0IG92ZXJDb250YWluZXIgPSBvdmVyLmRhdGE/LmN1cnJlbnQ/LnNvcnRhYmxlPy5jb250YWluZXJJZCB8fCBvdmVyLmlkO1xuICAgIGxldCBuZXdTdGF0dXMgPSBhY3RpdmVUYXNrLnN0YXR1cztcbiAgICBpZiAob3ZlckNvbnRhaW5lci5pbmNsdWRlcygndG9kbycpKSBuZXdTdGF0dXMgPSAndG9kbyc7XG4gICAgZWxzZSBpZiAob3ZlckNvbnRhaW5lci5pbmNsdWRlcygnaW4tcHJvZ3Jlc3MnKSkgbmV3U3RhdHVzID0gJ2luLXByb2dyZXNzJztcbiAgICBlbHNlIGlmIChvdmVyQ29udGFpbmVyLmluY2x1ZGVzKCdkb25lJykpIG5ld1N0YXR1cyA9ICdkb25lJztcbiAgICBpZiAobmV3U3RhdHVzICE9PSBhY3RpdmVUYXNrLnN0YXR1cykge1xuICAgICAgbW92ZVRhc2soYWN0aXZlLmlkLCBuZXdTdGF0dXMpO1xuICAgICAgdG9hc3Quc3VjY2VzcyhgVGFzayBtb3ZlZCB0byAke25ld1N0YXR1cy5yZXBsYWNlKCctJywgJyAnKX1gKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IGFjdGl2ZVRhc2sgPSBhY3RpdmVJZCA/IHByb2plY3RUYXNrcy5maW5kKHRhc2sgPT4gdGFzay5pZCA9PT0gYWN0aXZlSWQpIDogbnVsbDtcblxuICBjb25zdCBoYW5kbGVFeHBvcnRDU1YgPSAoKSA9PiB7XG4gICAgY29uc3Qgc3VjY2VzcyA9IGV4cG9ydFRhc2tzVG9DU1YocHJvamVjdFRhc2tzLCBgJHtjdXJyZW50UHJvamVjdH0ta2FuYmFuLXRhc2tzYCk7XG4gICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1Rhc2tzIGV4cG9ydGVkIHRvIENTViBzdWNjZXNzZnVsbHkhJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gZXhwb3J0IHRhc2tzJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydFBERiA9ICgpID0+IHtcbiAgICBjb25zdCBzdGF0cyA9IGdldFRhc2tTdGF0cygpO1xuICAgIGNvbnN0IHN1Y2Nlc3MgPSBleHBvcnRUYXNrc1RvUERGKHByb2plY3RUYXNrcywgc3RhdHMsIGAke2N1cnJlbnRQcm9qZWN0fS1rYW5iYW4tcmVwb3J0YCk7XG4gICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoJ1Rhc2tzIGV4cG9ydGVkIHRvIFBERiBzdWNjZXNzZnVsbHkhJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gZXhwb3J0IHRhc2tzJyk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIEthbmJhbiBCb2FyZFxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgIERyYWcgYW5kIGRyb3AgdGFza3MgYmV0d2VlbiBjb2x1bW5zIHRvIHVwZGF0ZSB0aGVpciBzdGF0dXNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgb25DbGljaz17aGFuZGxlRXhwb3J0Q1NWfT5cbiAgICAgICAgICAgICAgRXhwb3J0IENTVlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgb25DbGljaz17aGFuZGxlRXhwb3J0UERGfT5cbiAgICAgICAgICAgICAgRXhwb3J0IFBERlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgICA8RG5kQ29udGV4dFxuICAgICAgICBzZW5zb3JzPXtzZW5zb3JzfVxuICAgICAgICBjb2xsaXNpb25EZXRlY3Rpb249e2Nsb3Nlc3RDb3JuZXJzfVxuICAgICAgICBvbkRyYWdTdGFydD17aGFuZGxlRHJhZ1N0YXJ0fVxuICAgICAgICBvbkRyYWdFbmQ9e2hhbmRsZURyYWdFbmR9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNiBvdmVyZmxvdy14LWF1dG8gcGItNFwiPlxuICAgICAgICAgIDxLYW5iYW5Db2x1bW5cbiAgICAgICAgICAgIHRpdGxlPVwiVG8gRG9cIlxuICAgICAgICAgICAgc3RhdHVzPVwidG9kb1wiXG4gICAgICAgICAgICB0YXNrcz17dG9kb1Rhc2tzfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPEthbmJhbkNvbHVtblxuICAgICAgICAgICAgdGl0bGU9XCJJbiBQcm9ncmVzc1wiXG4gICAgICAgICAgICBzdGF0dXM9XCJpbi1wcm9ncmVzc1wiXG4gICAgICAgICAgICB0YXNrcz17aW5Qcm9ncmVzc1Rhc2tzfVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPEthbmJhbkNvbHVtblxuICAgICAgICAgICAgdGl0bGU9XCJEb25lXCJcbiAgICAgICAgICAgIHN0YXR1cz1cImRvbmVcIlxuICAgICAgICAgICAgdGFza3M9e2RvbmVUYXNrc31cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPERyYWdPdmVybGF5PlxuICAgICAgICAgIHthY3RpdmVUYXNrID8gPFRhc2tDYXJkIHRhc2s9e2FjdGl2ZVRhc2t9IC8+IDogbnVsbH1cbiAgICAgICAgPC9EcmFnT3ZlcmxheT5cbiAgICAgIDwvRG5kQ29udGV4dD5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkRuZENvbnRleHQiLCJEcmFnT3ZlcmxheSIsImNsb3Nlc3RDb3JuZXJzIiwiS2V5Ym9hcmRTZW5zb3IiLCJQb2ludGVyU2Vuc29yIiwidXNlU2Vuc29yIiwidXNlU2Vuc29ycyIsImFycmF5TW92ZSIsIlNvcnRhYmxlQ29udGV4dCIsInNvcnRhYmxlS2V5Ym9hcmRDb29yZGluYXRlcyIsInZlcnRpY2FsTGlzdFNvcnRpbmdTdHJhdGVneSIsInVzZVNvcnRhYmxlIiwiQ1NTIiwidXNlVGFza1N0b3JlIiwidXNlVGltZVRyYWNraW5nU3RvcmUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJCdXR0b24iLCJNb2RhbCIsIklucHV0IiwiQ2xvY2siLCJDYWxlbmRhciIsIkVkaXQiLCJUcmFzaDIiLCJQbGF5IiwiUGF1c2UiLCJNb3JlSG9yaXpvbnRhbCIsImZvcm1hdERhdGUiLCJnZXRQcmlvcml0eUNvbG9yIiwiZXhwb3J0VGFza3NUb0NTViIsImV4cG9ydFRhc2tzVG9QREYiLCJ0b2FzdCIsIlRhc2tDYXJkIiwidGFzayIsInVwZGF0ZVRhc2siLCJkZWxldGVUYXNrIiwiYWN0aXZlVGltZXIiLCJzdGFydFRpbWVyIiwic3RvcFRpbWVyIiwic2hvd0VkaXQiLCJzZXRTaG93RWRpdCIsImVkaXRUYXNrIiwic2V0RWRpdFRhc2siLCJhdHRyaWJ1dGVzIiwibGlzdGVuZXJzIiwic2V0Tm9kZVJlZiIsInRyYW5zZm9ybSIsInRyYW5zaXRpb24iLCJpc0RyYWdnaW5nIiwiaWQiLCJzdHlsZSIsIlRyYW5zZm9ybSIsInRvU3RyaW5nIiwiaGFuZGxlRWRpdCIsInN1Y2Nlc3MiLCJoYW5kbGVEZWxldGUiLCJ3aW5kb3ciLCJjb25maXJtIiwiaGFuZGxlVGltZXIiLCJ0YXNrSWQiLCJ0aXRsZSIsImlzVGltZXJBY3RpdmUiLCJkaXYiLCJyZWYiLCJjbGFzc05hbWUiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiZGVzY3JpcHRpb24iLCJwIiwicHJpb3JpdHkiLCJkZWFkbGluZSIsImlzT3BlbiIsIm9uQ2xvc2UiLCJsYWJlbCIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJ0ZXh0YXJlYSIsInJvd3MiLCJzZWxlY3QiLCJvcHRpb24iLCJ0eXBlIiwiS2FuYmFuQ29sdW1uIiwic3RhdHVzIiwidGFza3MiLCJpY29uIiwiSWNvbiIsImNvbG9yIiwiaXNPdmVyIiwidXNlRHJvcHBhYmxlIiwiY29sb3JDbGFzc2VzIiwiYmx1ZSIsInllbGxvdyIsImdyZWVuIiwiaDMiLCJsZW5ndGgiLCJpdGVtcyIsIm1hcCIsInN0cmF0ZWd5IiwiS2FuYmFuQm9hcmQiLCJtb3ZlVGFzayIsImN1cnJlbnRQcm9qZWN0IiwiZ2V0VGFza1N0YXRzIiwiYWN0aXZlSWQiLCJzZXRBY3RpdmVJZCIsInNlbnNvcnMiLCJjb29yZGluYXRlR2V0dGVyIiwicHJvamVjdFRhc2tzIiwiZmlsdGVyIiwicHJvamVjdCIsInRvZG9UYXNrcyIsImluUHJvZ3Jlc3NUYXNrcyIsImRvbmVUYXNrcyIsImhhbmRsZURyYWdTdGFydCIsImV2ZW50IiwiYWN0aXZlIiwiaGFuZGxlRHJhZ0VuZCIsIm92ZXIiLCJhY3RpdmVUYXNrIiwiZmluZCIsIm92ZXJDb250YWluZXIiLCJkYXRhIiwiY3VycmVudCIsInNvcnRhYmxlIiwiY29udGFpbmVySWQiLCJuZXdTdGF0dXMiLCJpbmNsdWRlcyIsInJlcGxhY2UiLCJoYW5kbGVFeHBvcnRDU1YiLCJlcnJvciIsImhhbmRsZUV4cG9ydFBERiIsInN0YXRzIiwiaDIiLCJjb2xsaXNpb25EZXRlY3Rpb24iLCJvbkRyYWdTdGFydCIsIm9uRHJhZ0VuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/KanbanBoard.js\n"));

/***/ })

});