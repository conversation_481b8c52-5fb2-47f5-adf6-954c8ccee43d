import { Parser } from 'json2csv';
import jsPDF from 'jspdf';
export const exportTasksToCSV = (tasks, filename = 'focusflow-tasks') => {
  try {
    const fields = [
      { label: 'Title', value: 'title' },
      { label: 'Description', value: 'description' },
      { label: 'Status', value: 'status' },
      { label: 'Priority', value: 'priority' },
      { label: 'Deadline', value: 'deadline' },
      { label: 'Created At', value: 'createdAt' },
      { label: 'Updated At', value: 'updatedAt' },
      { label: 'Time Spent (minutes)', value: 'timeSpent' }
    ];
    const parser = new Parser({ fields });
    const csv = parser.parse(tasks);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    return true;
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    return false;
  }
};
export const exportTasksToPDF = (tasks, analytics, filename = 'focusflow-report') => {
  try {
    const doc = new jsPDF();
    doc.setFontSize(20);
    doc.text('FocusFlow AI - Productivity Report', 20, 20);
    doc.setFontSize(12);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 35);
    if (analytics) {
      doc.setFontSize(16);
      doc.text('Analytics Summary', 20, 55);
      doc.setFontSize(12);
      doc.text(`Total Tasks: ${analytics.totalTasks}`, 20, 70);
      doc.text(`Completed Tasks: ${analytics.completedTasks}`, 20, 80);
      doc.text(`In Progress: ${analytics.inProgressTasks}`, 20, 90);
      doc.text(`Completion Rate: ${analytics.completionRate}%`, 20, 100);
      doc.text(`Total Time Spent: ${analytics.totalTimeSpent} minutes`, 20, 110);
    }
    doc.setFontSize(16);
    doc.text('Task Details', 20, 130);
    let yPosition = 145;
    tasks.forEach((task, index) => {
      if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;
      }
      doc.setFontSize(12);
      doc.text(`${index + 1}. ${task.title}`, 20, yPosition);
      yPosition += 10;
      doc.setFontSize(10);
      doc.text(`Status: ${task.status} | Priority: ${task.priority}`, 25, yPosition);
      yPosition += 8;
      if (task.description) {
        const splitDescription = doc.splitTextToSize(task.description, 160);
        doc.text(splitDescription, 25, yPosition);
        yPosition += splitDescription.length * 5;
      }
      if (task.deadline) {
        doc.text(`Deadline: ${new Date(task.deadline).toLocaleDateString()}`, 25, yPosition);
        yPosition += 8;
      }
      yPosition += 5;
    });
    doc.save(`${filename}.pdf`);
    return true;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return false;
  }
};
export const exportAnalyticsToPDF = (analytics, filename = 'focusflow-analytics') => {
  try {
    const doc = new jsPDF();
    doc.setFontSize(20);
    doc.text('FocusFlow AI - Analytics Report', 20, 20);
    doc.setFontSize(12);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 35);
    doc.setFontSize(16);
    doc.text('Productivity Metrics', 20, 55);
    doc.setFontSize(12);
    let yPos = 70;
    Object.entries(analytics).forEach(([key, value]) => {
      const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      doc.text(`${formattedKey}: ${value}`, 20, yPos);
      yPos += 15;
    });
    doc.save(`${filename}.pdf`);
    return true;
  } catch (error) {
    console.error('Error exporting analytics to PDF:', error);
    return false;
  }
};
