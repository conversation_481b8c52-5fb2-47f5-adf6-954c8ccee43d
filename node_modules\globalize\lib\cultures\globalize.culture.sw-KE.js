/*
 * Globalize Culture sw-KE
 *
 * http://github.com/jquery/globalize
 *
 * Copyright Software Freedom Conservancy, Inc.
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * This file was generated by the Globalize Culture Generator
 * Translation: bugs found in this file need to be fixed in the generator
 */

(function( window, undefined ) {

var Globalize;

if ( typeof require !== "undefined" &&
	typeof exports !== "undefined" &&
	typeof module !== "undefined" ) {
	// Assume CommonJS
	Globalize = require( "globalize" );
} else {
	// Global variable
	Globalize = window.Globalize;
}

Globalize.addCultureInfo( "sw-KE", "default", {
	name: "sw-K<PERSON>",
	englishName: "Kiswahili (Kenya)",
	nativeName: "Kiswahili (Kenya)",
	language: "sw",
	numberFormat: {
		currency: {
			symbol: "S"
		}
	},
	calendars: {
		standard: {
			days: {
				names: ["<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],
				namesAbbr: ["<PERSON>ma<PERSON>.","<PERSON><PERSON>.","<PERSON><PERSON>.","<PERSON><PERSON>.","<PERSON>h.","<PERSON><PERSON>.","Juma<PERSON>."],
				namesShort: ["P","T","N","T","A","I","M"]
			},
			months: {
				names: ["<PERSON>uari","Februari","<PERSON>hi","Aprili","<PERSON>","<PERSON>i","Julai","A<PERSON>ti","Septemba","<PERSON>toba","Novemba","<PERSON>emba",""],
				names<PERSON>bbr: ["<PERSON>","Feb","Mac","Apr","Mei","Jun","Jul","Ago","Sep","Okt","Nov","Dec",""]
			}
		}
	}
});

}( this ));
